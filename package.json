{"name": "next-js-starter-pack", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "turbo": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format:write": "prettier --write .", "prepare": "husky", "test": "tsc --noEmit"}, "dependencies": {"@commitlint/types": "^19.5.0", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@t3-oss/env-nextjs": "^0.11.1", "@tanstack/react-query": "^5.64.1", "axios": "^1.7.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "cookies-next": "^4.3.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "eslint-config-airbnb-typescript": "^18.0.0", "input-otp": "^1.2.4", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.439.0", "next": "14.2.7", "next-intl": "^3.26.5", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-infinite-scroll-component": "^6.1.0", "react-loading-skeleton": "^3.5.0", "react-resizable-panels": "^2.1.3", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.23.8"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{jsx,js,ts,tsx}": ["eslint --cache --fix", "prettier --write"]}, "devDependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@svgr/webpack": "^8.1.0", "@types/node": "^20.16.5", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-airbnb": "19.0.4", "eslint-config-next": "14.2.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.36.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.17.4", "husky": "^9.1.6", "lint-staged": "^15.2.10", "postcss": "^8.4.47", "prettier": "3.3.3", "tailwindcss": "^3.4.11", "typescript": "^5.6.2"}}