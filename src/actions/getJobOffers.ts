import { OffersResponse } from '@components/Offers/types';

import { queryWithErrorToast } from './queryWithErrorToast';

const OFFERS_PER_PAGE = 10;

export interface JobOffersFilters {
  currentPage: number;
  categoryIds?: number[];
  position?: string;
  minSalary?: number;
  maxSalary?: number;
  cityIds?: number[]; // Dodane dla przyszłości
}

export const getJobOffers = async (filters: JobOffersFilters) => {
  const { currentPage, categoryIds, position, minSalary, maxSalary, cityIds } =
    filters;

  const params = new URLSearchParams({
    numberOfOffers: OFFERS_PER_PAGE.toString(),
    currentPage: currentPage.toString(),
  });

  if (categoryIds && categoryIds.length > 0) {
    categoryIds.forEach((id) => {
      params.append('categoryIds', id.toString());
    });
  }

  if (position) {
    params.append('position', position);
  }

  if (minSalary) {
    params.append('minSalary', minSalary.toString());
  }

  if (maxSalary) {
    params.append('maxSalary', maxSalary.toString());
  }

  // Obsługa wielu cityIds (dla przyszłości)
  if (cityIds && cityIds.length > 0) {
    cityIds.forEach((id) => {
      params.append('cityIds', id.toString());
    });
  }

  return queryWithErrorToast<OffersResponse>({
    method: 'get',
    url: `/job-offers?${params.toString()}`,
  });
};
