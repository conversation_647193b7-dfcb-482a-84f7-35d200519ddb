import { useTranslations } from 'next-intl';

import { CompanyDataProps } from '@components/Company/types';

import { queryWithErrorToast } from '@actions/queryWithErrorToast';
import { handleSetCompanyId } from '@actions/server/login';
import { toast, ToastIconVariant } from '@hooks/use-toast';

export const useCreateCompany = () => {
  const t = useTranslations('Toasts');

  const createCompany = async (props: CompanyDataProps) => {
    const { data } = await queryWithErrorToast<{ id: string }>({
      method: 'post',
      url: `/companies`,
      authorized: true,
      data: props,
    });

    const companyId = data?.id;

    if (companyId) {
      handleSetCompanyId(companyId);

      toast({
        iconVariant: ToastIconVariant.SUCCESS,
        title: t('success'),
      });
    }

    return { data };
  };

  return createCompany;
};
