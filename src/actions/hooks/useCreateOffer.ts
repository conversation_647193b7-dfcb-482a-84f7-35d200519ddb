import { useTranslations } from 'next-intl';

import { queryWithErrorToast } from '@actions/queryWithErrorToast';
import { toast, ToastIconVariant } from '@hooks/use-toast';

export const useCreateOffer = () => {
  const t = useTranslations('Toasts');

  const createOffer = async (props: any) => {
    const { data } = await queryWithErrorToast<{ id: string }>({
      method: 'post',
      url: `/job-offers`,
      data: props,
      authorized: true,
    });

    if (data) {
      toast({
        iconVariant: ToastIconVariant.SUCCESS,
        title: t('success'),
      });
    } else {
      throw new Error();
    }

    return {
      data,
    };
  };

  return createOffer;
};
