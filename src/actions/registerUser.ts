import { toast, ToastIconVariant } from '@hooks/use-toast';

import { queryWithErrorToast } from './queryWithErrorToast';
import { handleToken } from './server/login';

type Props = {
  email: string;
  password: string;
  name: string;
};

export const registerUser = async (props: Props) => {
  const { data } = await queryWithErrorToast<any>({
    method: 'post',
    url: `/auth/register`,
    data: props,
  });

  const authorizationToken = data?.token;

  if (!authorizationToken) {
    toast({
      iconVariant: ToastIconVariant.ERROR,
      description: 'No authorization token',
    });

    return null;
  }

  return handleToken(authorizationToken);
};
