'use server';

import { cookies } from 'next/headers';

import axios, { AxiosResponse } from 'axios';
import { jwtDecode } from 'jwt-decode';

export const handleSetCompanyId = (id: string) => {
  cookies().set('companyId', id, {
    path: '/',
    secure: true,
  });
};

export const handleToken = (authorizationToken: string, companyId?: string) => {
  const decoded = jwtDecode<{
    iat: number;
    exp: number;
    user_metadata: {
      sub: string;
    };
  }>(authorizationToken);

  if (!decoded) return null;

  const {
    iat,
    exp,
    user_metadata: { sub: userUuid },
  } = decoded;

  const maxAge = exp - iat;

  cookies().set('token', authorizationToken, {
    path: '/',
    maxAge,
    secure: true,
    httpOnly: true,
  });
  cookies().set('userUuid', userUuid, {
    path: '/',
    maxAge,
    secure: true,
  });

  if (companyId) {
    handleSetCompanyId(companyId);
  }

  return { userUuid };
};

export const login = async (data: {
  email: string;
  password: string;
}): Promise<{ userUuid?: string; errorMessage?: string } | null> => {
  try {
    const response: AxiosResponse<any> = await axios({
      method: 'post',
      url: `${process.env.NEXT_PUBLIC_API_URL}/auth/login`,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      data,
    });

    const authorizationToken = response.data?.token;
    const companyId = response.data?.company?.id;

    if (!authorizationToken)
      return { errorMessage: 'Unexpected error occurred while logging in.' };

    return handleToken(authorizationToken, companyId);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return { errorMessage: error.message };
    }

    return { errorMessage: 'Unexpected error occurred while logging in.' };
  }
};
