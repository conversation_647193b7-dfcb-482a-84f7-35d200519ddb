import { useState } from 'react';

import { Button } from '@app/elements/Buttons';
import { TextFieldTextArea } from '@app/elements/TextFields';

import { ModalAboutUsProps } from './types';
import { useModal } from '../context/ModalContext';

export function ModalAboutUs({ defaultValue, onSave }: ModalAboutUsProps) {
  const { handleVariableModalSave, modalDefaultValue } = useModal();
  const [about, setAbout] = useState<string>(
    defaultValue ?? modalDefaultValue ?? ''
  );

  const handleSave = () => {
    if (onSave) {
      onSave(about);
    } else if (handleVariableModalSave) {
      handleVariableModalSave(about);
    }
  };

  return (
    <>
      <TextFieldTextArea
        title="O Nas"
        value={about}
        setValue={setAbout}
        placeholder="Wprowadź tekst"
        borderRadius="rounded"
        fullWidth
      />
      <Button borderRadius="rounded" fullWidth onClick={handleSave}>
        Zapisz
      </Button>
    </>
  );
}
