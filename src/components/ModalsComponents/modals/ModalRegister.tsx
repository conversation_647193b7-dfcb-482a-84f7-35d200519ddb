import { FormEvent, useCallback, useState } from 'react';

import { useTranslations } from 'next-intl';

import { Button } from '@app/elements/Buttons';
import { TextField, TextFieldWithIcon } from '@app/elements/TextFields';
import { Text, TLink } from '@app/elements/Texts';

import { registerUser } from '@actions/registerUser';
import { toast, ToastIconVariant } from '@hooks/use-toast';

const ACCOUNT_TYPE_OPTIONS = [
  { id: 'employer', text: 'Oferuję pracę' },
  { id: 'candidate', text: 'Szukam pracy' },
];

export function ModalRegister() {
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [name, setName] = useState<string>('');
  const [accountType, setAccountType] = useState<string>('employer');
  const [isAccountTypeDropdownOpen, setIsAccountTypeDropdownOpen] =
    useState<boolean>(false);

  const t = useTranslations('Modals');
  const tToasts = useTranslations('Toasts');

  const getAccountTypeDisplayText = () => {
    const selectedOption = ACCOUNT_TYPE_OPTIONS.find(
      (option) => option.id === accountType
    );
    return selectedOption ? selectedOption.text : 'Wybierz typ konta';
  };

  const handleAccountTypeSelect = (selectedId: string) => {
    setAccountType(selectedId);
    setIsAccountTypeDropdownOpen(false);
  };

  const handleSubmit = useCallback(
    async (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();

      try {
        const response = await registerUser({
          email,
          password,
          name,
        });

        if (response?.userUuid) {
          window.location.reload();
        }
      } catch (error) {
        toast({
          iconVariant: ToastIconVariant.ERROR,
          title: tToasts('error'),
        });
      }
    },
    [email, password, name, tToasts]
  );

  return (
    <form
      onSubmit={handleSubmit}
      autoComplete="off"
      className="flex flex-col gap-4"
    >
      <div className="relative">
        <TextFieldWithIcon
          title="Typ konta"
          input={getAccountTypeDisplayText()}
          setInput={() => {}}
          icon="arrowDropDown"
          iconSize="sm"
          fullWidth
          placeholder="Wybierz typ konta"
          onClick={() =>
            setIsAccountTypeDropdownOpen(!isAccountTypeDropdownOpen)
          }
          readOnly
        />

        {isAccountTypeDropdownOpen && (
          <div className="absolute inset-x-0 top-full z-50 rounded-xl border border-tf-border bg-[#9e9b9b] shadow-xl">
            {ACCOUNT_TYPE_OPTIONS.map(({ id, text }, index) => (
              <div
                key={id}
                role="button"
                tabIndex={0}
                className={`cursor-pointer px-4 py-3 text-white transition-colors hover:bg-white/10 focus:bg-white/10 focus:outline-none ${
                  index === 0 ? 'rounded-t-xl' : ''
                } ${
                  index === ACCOUNT_TYPE_OPTIONS.length - 1
                    ? 'rounded-b-xl'
                    : ''
                }`}
                onClick={() => handleAccountTypeSelect(id)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleAccountTypeSelect(id);
                  }
                }}
              >
                {text}
              </div>
            ))}
          </div>
        )}
      </div>
      <TextField
        title={t('name')}
        input={name}
        setInput={setName}
        placeholder={t('namePlaceholder')}
        borderRadius="rounded"
      />

      <TextField
        title={t('email')}
        input={email}
        setInput={setEmail}
        placeholder={t('emailExample')}
        borderRadius="rounded"
      />

      <TextField
        title={t('password')}
        input={password}
        setInput={setPassword}
        placeholder={t('passwordPlaceholder')}
        borderRadius="rounded"
        inputType="password"
      />

      <div className="p-2">
        <Text align="center" fontSize="md">
          {t.rich('acceptTerms', {
            link: (chunks) => <TLink href="">{chunks}</TLink>,
          })}
        </Text>
      </div>

      <Button borderRadius="rounded" fullWidth padding="lg" type="submit">
        {t('register')}
      </Button>
    </form>
  );
}
