import { Dispatch, SetStateAction } from 'react';

import { OfferDetailedProps } from '@components/Offers/types';

import { CategorySelection } from './CategorySelector';

export interface ApplicationModalProps {
  closeModal: () => void;
  onSuccess: () => void;
  offer: OfferDetailedProps;
}

export interface ApplicationSuccesModalProps {
  closeModal: () => void;
}
export interface CompanySizeSelectorProps {
  currentSize: string;
  onSizeSelect: Dispatch<SetStateAction<string>>;
  onClose: () => void;
}

export interface LocationSelectorProps {
  onSelect: (locations: string[]) => void;
  selectedLocations?: string[];
}

export interface ModalAboutUsProps {
  defaultValue?: string;
  onSave?: (value: string) => void;
}

export interface CompanyFormData {
  name: string;
  website?: string;
  size?: string;
  aboutCompany?: string;
}

export interface FilterData {
  category?: CategorySelection | null;
  location?: string;
  salary: {
    from: string;
    to: string;
  };
}
