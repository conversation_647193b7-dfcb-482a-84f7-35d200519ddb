import React, { useMemo, useState } from 'react';

import { Button, ButtonAsIcon } from '@app/elements/Buttons';
import { TextField } from '@app/elements/TextFields';
import { Text } from '@app/elements/Texts';

import { useModal } from '../context/ModalContext';

type ModalListType = 'expectations' | 'offer' | 'daily_tasks';

export function ModalExpectationsRequirements() {
  const { handleVariableModalSave, modalDefaultValue, currentModalType } =
    useModal();

  const listType: ModalListType = useMemo(() => {
    switch (currentModalType) {
      case 'expectations':
        return 'expectations';
      case 'daily_tasks':
        return 'daily_tasks';
      default:
        return 'offer';
    }
  }, [currentModalType]);

  const config = {
    expectations: {
      title: 'Brak podanych oczekiwań',
      instruction: 'Dodaj oczekiwanie',
      placeholder: 'Podaj oczekiwanie',
      buttonText: 'Dodaj oczekiwanie',
    },
    offer: {
      title: '<PERSON>rak podanych elementów oferty',
      instruction: '<PERSON>daj ofertę',
      placeholder: '<PERSON>daj ofertę',
      buttonText: 'Dodaj ofertę',
    },
    daily_tasks: {
      title: 'Brak podanych zadań',
      instruction: 'Dodaj zadanie',
      placeholder: 'Podaj zadanie',
      buttonText: 'Dodaj zadanie',
    },
  };

  const [items, setItems] = useState<string[]>(
    Array.isArray(modalDefaultValue) ? modalDefaultValue : []
  );

  const [newItem, setNewItem] = useState('');

  const handleAddItem = () => {
    if (newItem.trim()) {
      setItems([...items, newItem.trim()]);
      setNewItem('');
    }
  };

  const handleRemoveItem = (index: number) => {
    const updatedItems = [...items];
    updatedItems.splice(index, 1);
    setItems(updatedItems);
  };

  const handleSave = () => {
    handleVariableModalSave?.(items);
  };

  const { title, instruction, placeholder, buttonText } = config[listType];

  return (
    <div className="flex flex-col gap-3">
      {items.length === 0 ? (
        <div className="relative py-2">
          <span className="absolute top-3 h-16 w-1.5 rounded-md bg-slate-50 max-laptop:h-12" />
          <div className="ml-4">
            <Text fontSize="xl" fontWeight="semibold" className="text-white">
              {title}
            </Text>
            <Text fontSize="lg" className="text-white text-opacity-50">
              Kliknij przycisk
              <strong> {instruction}</strong>
            </Text>
          </div>
        </div>
      ) : (
        <div
          style={{
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
          }}
          className="flex max-h-[300px] flex-col gap-2 overflow-y-auto overflow-x-hidden"
        >
          {items.map((item, index) => (
            <div key={index} className="flex items-center gap-2">
              <div className="mb-1 flex-1 rounded-xl bg-white/10 p-3.5">
                <Text fontSize="md" className="text-white">
                  {item}
                </Text>
              </div>
              <ButtonAsIcon
                buttonIcon="trash"
                iconSize="md"
                variant="secondary"
                onClick={() => handleRemoveItem(index)}
              />
            </div>
          ))}
        </div>
      )}

      <div className="flex flex-col gap-4">
        <TextField
          className="flex-1 rounded-xl"
          input={newItem}
          setInput={setNewItem}
          padding="lg"
          textSize="sm"
          placeholder={placeholder}
        />
        <Button
          onClick={handleAddItem}
          padding="lg"
          fullWidth
          disabled={!newItem.trim()}
        >
          {buttonText}
        </Button>
      </div>

      <Button
        borderRadius="rounded"
        fullWidth
        padding="lg"
        onClick={handleSave}
      >
        Zapisz
      </Button>
    </div>
  );
}
