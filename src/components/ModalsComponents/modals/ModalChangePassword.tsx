import { FormEvent, useCallback, useState } from 'react';

import { useTranslations } from 'next-intl';

import { Button } from '@app/elements/Buttons';
import { TextField } from '@app/elements/TextFields';
import { Text } from '@app/elements/Texts';

import { toast, ToastIconVariant } from '@hooks/use-toast';

import { useModal } from '../context/ModalContext';

export function ModalChangePassword() {
  const { closeModal } = useModal();
  const t = useTranslations('Modals');
  const tToasts = useTranslations('Toasts');

  const [currentPassword, setCurrentPassword] = useState<string>('');
  const [newPassword, setNewPassword] = useState<string>('');
  const [confirmPassword, setConfirmPassword] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleSubmit = useCallback(
    async (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();

      // Walidacja
      if (!currentPassword.trim()) {
        toast({
          iconVariant: ToastIconVariant.ERROR,
          title: tToasts('error'),
          description: 'Wprowadź obecne hasło',
        });
        return;
      }

      if (!newPassword.trim()) {
        toast({
          iconVariant: ToastIconVariant.ERROR,
          title: tToasts('error'),
          description: 'Wprowadź nowe hasło',
        });
        return;
      }

      if (newPassword !== confirmPassword) {
        toast({
          iconVariant: ToastIconVariant.ERROR,
          title: tToasts('error'),
          description: 'Nowe hasła nie są identyczne',
        });
        return;
      }

      if (newPassword.length < 6) {
        toast({
          iconVariant: ToastIconVariant.ERROR,
          title: tToasts('error'),
          description: 'Nowe hasło musi mieć co najmniej 6 znaków',
        });
        return;
      }

      setIsLoading(true);

      try {
        // TODO: Implementacja API do zmiany hasła
        // const response = await changePassword({
        //   currentPassword,
        //   newPassword,
        // });

        // Tymczasowo symulujemy sukces
        await new Promise(resolve => setTimeout(resolve, 1000));

        toast({
          iconVariant: ToastIconVariant.SUCCESS,
          title: tToasts('success'),
          description: 'Hasło zostało pomyślnie zmienione',
        });

        closeModal();
      } catch (error) {
        toast({
          iconVariant: ToastIconVariant.ERROR,
          title: tToasts('error'),
          description: 'Wystąpił błąd podczas zmiany hasła',
        });
      } finally {
        setIsLoading(false);
      }
    },
    [currentPassword, newPassword, confirmPassword, tToasts, closeModal]
  );

  return (
    <form
      onSubmit={handleSubmit}
      autoComplete="off"
      className="flex flex-col gap-4"
    >
      <Text fontSize="lg" className="font-semibold text-white text-center">
        Zmień hasło
      </Text>

      <TextField
        title="Obecne hasło"
        input={currentPassword}
        setInput={setCurrentPassword}
        placeholder="Wprowadź obecne hasło"
        borderRadius="rounded"
        inputType="password"
        fullWidth
      />

      <TextField
        title="Nowe hasło"
        input={newPassword}
        setInput={setNewPassword}
        placeholder="Wprowadź nowe hasło"
        borderRadius="rounded"
        inputType="password"
        fullWidth
      />

      <TextField
        title="Powtórz nowe hasło"
        input={confirmPassword}
        setInput={setConfirmPassword}
        placeholder="Powtórz nowe hasło"
        borderRadius="rounded"
        inputType="password"
        fullWidth
      />

      <Button 
        borderRadius="rounded" 
        fullWidth 
        padding="lg" 
        type="submit"
        disabled={isLoading}
      >
        {isLoading ? 'Zmieniam hasło...' : 'Zmień hasło'}
      </Button>
    </form>
  );
}
