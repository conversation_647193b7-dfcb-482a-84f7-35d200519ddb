import { Dispatch, SetStateAction, useState, useEffect } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { useQueryClient } from '@tanstack/react-query';

import { Button } from '@app/elements/Buttons';
import { TextField } from '@app/elements/TextFields';

import { useModal } from '@components/ModalsComponents/context/ModalContext';
import PriceRange from '@components/PriceRange/PriceRange';

import { useCategories } from '@actions/hooks/useCategories';
import { useLocations } from '@actions/hooks/useLocations';

import { CategorySelection } from './CategorySelector';
import { CitySelector } from './CitySelector';
import { ModalCategory } from './ModalCategory';
import { FilterData } from './types';

const initialFilterData: FilterData = {
  category: null,
  location: '',
  salary: {
    from: '',
    to: '',
  },
};

export function ModalFilter() {
  const {
    openNestedModal,
    isNestedModalOpen,
    currentNestedModalType,
    closeNestedModal,
    closeModal,
  } = useModal();
  const { data } = useLocations();
  const locations = data?.data || [];
  const { data: categoriesData } = useCategories();
  const categories = categoriesData?.data || [];
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  const [filterData, setFilterData] = useState<FilterData>(initialFilterData);
  const [isSearching, setIsSearching] = useState(false);

  const findCategoryBySubcategoryId = (subcategoryId: number) => {
    for (const category of categories) {
      const subcategory = category.subcategories?.find(
        (sub) => sub.id === subcategoryId
      );
      if (subcategory) {
        return { category, subcategory };
      }
    }
    return null;
  };

  const findCategoryById = (categoryId: number) => {
    const category = categories.find((cat) => cat.id === categoryId);
    if (category) {
      return { category, subcategory: null }; // null = cała kategoria
    }
    return null;
  };

  useEffect(() => {
    const categoryIdsParam = searchParams.get('categoryIds');
    const minSalary = searchParams.get('minSalary');
    const maxSalary = searchParams.get('maxSalary');

    if (categoryIdsParam || minSalary || maxSalary) {
      let categorySelection: CategorySelection | null = null;

      if (categoryIdsParam && categories.length > 0) {
        const categoryIds = categoryIdsParam.split(',').map(Number);

        if (categoryIds.length === 1) {
          // Pojedyncze ID - sprawdź czy to subkategoria czy kategoria
          const categoryId = categoryIds[0];
          categorySelection = findCategoryBySubcategoryId(categoryId);

          if (!categorySelection) {
            categorySelection = findCategoryById(categoryId);
          }
        } else {
          // Wiele ID - sprawdzamy czy wszystkie należą do tej samej kategorii
          const firstSubcategory = findCategoryBySubcategoryId(categoryIds[0]);
          if (firstSubcategory) {
            const allFromSameCategory = categoryIds.every((id) => {
              const found = findCategoryBySubcategoryId(id);
              return (
                found && found.category.id === firstSubcategory.category.id
              );
            });

            if (allFromSameCategory) {
              // Sprawdź czy to wszystkie subkategorie z tej kategorii
              const categorySubcategoryIds =
                firstSubcategory.category.subcategories?.map((sub) => sub.id) ||
                [];
              const hasAllSubcategories =
                categorySubcategoryIds.length === categoryIds.length &&
                categorySubcategoryIds.every((id) => categoryIds.includes(id));

              if (hasAllSubcategories) {
                // To jest wybór całej kategorii
                categorySelection = {
                  category: firstSubcategory.category,
                  subcategory: null,
                };
              } else {
                // Fallback - pierwsza subkategoria
                categorySelection = firstSubcategory;
              }
            }
          }
        }
      }

      setFilterData((prev) => ({
        ...prev,
        category: categorySelection,
        salary: {
          from: minSalary || '',
          to: maxSalary || '',
        },
      }));
    } else {
      setFilterData(initialFilterData);
    }
  }, [searchParams, categories]);

  const getSalaryUpdater =
    (type: 'from' | 'to'): Dispatch<SetStateAction<string>> =>
    (value) => {
      const newValue =
        typeof value === 'function' ? value(filterData.salary[type]) : value;

      setFilterData((prev) => ({
        ...prev,
        salary: {
          ...prev.salary,
          [type]: newValue,
        },
      }));
    };

  const handleLocationSelect = (location?: string) => {
    setFilterData((prev) => ({ ...prev, location }));
    closeNestedModal();
  };

  const handleCategorySelect = (selection: CategorySelection | null) => {
    setFilterData((prev) => ({ ...prev, category: selection }));
    closeNestedModal();
  };

  const handleClear = async () => {
    setIsSearching(true);

    try {
      setFilterData(initialFilterData);

      const newParams = new URLSearchParams(searchParams);
      newParams.delete('categoryIds');
      newParams.delete('position');
      newParams.delete('minSalary');
      newParams.delete('maxSalary');
      newParams.delete('activeOffer');

      await queryClient.invalidateQueries({
        queryKey: ['get-job-offers'],
      });

      router.push(`/?${newParams.toString()}`);
      closeModal();
    } finally {
      setIsSearching(false);
    }
  };

  const handleSearch = async () => {
    setIsSearching(true);

    try {
      const newParams = new URLSearchParams(searchParams);

      newParams.delete('categoryIds');
      newParams.delete('position');
      newParams.delete('minSalary');
      newParams.delete('maxSalary');

      if (filterData.salary.from) {
        newParams.set('minSalary', filterData.salary.from);
      }

      if (filterData.salary.to) {
        newParams.set('maxSalary', filterData.salary.to);
      }

      if (filterData.category) {
        if (filterData.category.subcategory) {
          // Wybrana konkretna subkategoria
          newParams.set(
            'categoryIds',
            filterData.category.subcategory.id.toString()
          );
        } else {
          // Wybrana cała kategoria - dodaj wszystkie subkategorie tej kategorii
          const subcategoryIds =
            filterData.category.category.subcategories?.map((sub) =>
              sub.id.toString()
            ) || [];

          if (subcategoryIds.length > 0) {
            newParams.set('categoryIds', subcategoryIds.join(','));
          }
        }
      }

      newParams.delete('activeOffer');

      await queryClient.invalidateQueries({
        queryKey: ['get-job-offers'],
      });

      router.push(`/?${newParams.toString()}`);
      closeModal();
    } finally {
      setIsSearching(false);
    }
  };

  if (isNestedModalOpen && currentNestedModalType === 'city') {
    return (
      <CitySelector
        currentLocation={filterData.location}
        onSelect={handleLocationSelect}
      />
    );
  }

  if (isNestedModalOpen && currentNestedModalType === 'category') {
    return (
      <ModalCategory
        defaultValue={filterData.category}
        onSave={handleCategorySelect}
      />
    );
  }

  const location = (() => {
    if (!filterData.location) return '';
    return (
      locations.find(({ id }) => id === filterData.location)?.city.name || ''
    );
  })();

  const categoryDisplayName = (() => {
    if (!filterData.category) return '';
    const { category, subcategory } = filterData.category;
    return subcategory
      ? `${category.name} - ${subcategory.name}`
      : `Wszystko w "${category.name}"`;
  })();

  const hasActiveFilters =
    filterData.category !== null ||
    filterData.location !== '' ||
    filterData.salary.from !== '' ||
    filterData.salary.to !== '';

  return (
    <>
      <TextField
        title="Kategoria"
        input={categoryDisplayName}
        setInput={() => {}}
        placeholder="Wybierz specjalizację"
        borderRadius="rounded"
        onClick={() => openNestedModal('category')}
      />

      <TextField
        title="Lokalizacja"
        input={location}
        setInput={() => {}}
        placeholder="Wybierz lokalizację"
        borderRadius="rounded"
        onClick={() => openNestedModal('city')}
      />

      <PriceRange
        salaryFrom={filterData.salary.from}
        setSalaryFrom={getSalaryUpdater('from')}
        salaryTo={filterData.salary.to}
        setSalaryTo={getSalaryUpdater('to')}
      />

      <Button
        borderRadius="rounded"
        fullWidth
        padding="lg"
        onClick={handleSearch}
        disabled={isSearching}
      >
        {isSearching ? 'Wyszukiwanie...' : 'Wyszukaj'}
      </Button>

      {hasActiveFilters && (
        <Button
          variant="secondary"
          borderRadius="rounded"
          fullWidth
          padding="lg"
          onClick={handleClear}
          disabled={isSearching}
        >
          {isSearching ? 'Czyszczenie...' : 'Wyczyść wszystkie filtry'}
        </Button>
      )}
    </>
  );
}
