import React, { useCallback } from 'react';

import { useRouter } from 'next/navigation';

import { getCookie, setCookie } from 'cookies-next';
import { useTranslations } from 'next-intl';

import { borderRadiusStyles } from '@app/elements/_shared';
import { Button } from '@app/elements/Buttons';

import { useModal } from '@components/ModalsComponents/context/ModalContext';

import { logout } from '@actions/server/logout';
import { useUser } from '@contexts/UserContext';

export function ModalMenu() {
  const t = useTranslations('Menu');
  const router = useRouter();
  const { openModal } = useModal();
  const { user } = useUser();

  const locale = getCookie('NEXT_LOCALE') || 'pl';

  const setLanguage = useCallback(
    (newLocale: string) => () => {
      setCookie('NEXT_LOCALE', newLocale);
      router.refresh();
    },
    [router]
  );

  const handleLogout = async () => {
    await logout();
    router.push('/');
  };

  const handleCompanyClick = () => {
    const companyId = getCookie('companyId');
    if (companyId) {
      router.push(`/company/${companyId}`);
    }
  };

  return (
    <div className="flex flex-col gap-3.5">
      <div className="flex flex-col gap-2">
        <div
          className={`flex w-full gap-1 bg-[#FFFFFF10] p-1 ${borderRadiusStyles.rounded} overflow-hidden`}
        >
          <Button
            className={`font-normal ${
              locale === 'en'
                ? 'cursor-default bg-btn-primary-hover-bg'
                : 'bg-transparent'
            }`}
            onClick={locale !== 'en' ? setLanguage('en') : undefined}
            fullWidth
          >
            English
          </Button>

          <Button
            className={`font-normal ${
              locale === 'pl'
                ? 'cursor-default bg-btn-primary-hover-bg'
                : 'bg-transparent'
            }`}
            onClick={locale !== 'pl' ? setLanguage('pl') : undefined}
            fullWidth
          >
            Polski
          </Button>
        </div>
      </div>

      {user ? (
        <>
          <Button fullWidth>{t('profile')}</Button>
          <Button onClick={handleCompanyClick} fullWidth>
            {t('company')}
          </Button>
          <Button fullWidth>{t('addOffer')}</Button>
          <Button fullWidth>{t('regulations')}</Button>
          <Button onClick={() => openModal('change_password')} fullWidth>
            {t('changePassword')}
          </Button>
          <Button onClick={handleLogout} variant="secondary" fullWidth>
            {t('logout')}
          </Button>
        </>
      ) : (
        <Button
          onClick={() => openModal('login', ['login', 'signin'])}
          fullWidth
        >
          {t('login')}
        </Button>
      )}
    </div>
  );
}
