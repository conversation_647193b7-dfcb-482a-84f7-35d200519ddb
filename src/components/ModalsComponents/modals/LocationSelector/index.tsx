import { useEffect, useMemo, useState } from 'react';

import { Button } from '@app/elements/Buttons';
import { CheckRadio } from '@app/elements/Radio/CheckRadio';

import { useLocations } from '@actions/hooks/useLocations';

import { LocationSelectorProps } from '../types';
import { AddLocation } from './AddLocation';

export function LocationSelector({
  onSelect,
  selectedLocations: currentLocations = [],
}: LocationSelectorProps) {
  const locationsData = useLocations();
  const locations = useMemo(
    () => locationsData?.data?.data || [],
    [locationsData]
  );
  const { isLoading } = locationsData;
  const [addLocation, setAddLocation] = useState(
    !locations.length && !isLoading
  );

  useEffect(() => {
    if (!isLoading && !locations.length) {
      setAddLocation(true);
    }
  }, [locations, isLoading]);

  const [selectedLocations, setSelectedLocations] =
    useState<string[]>(currentLocations);

  const handleLocationClick = (id: string) => {
    const isSelected = selectedLocations.find((loc) => loc === id);
    setSelectedLocations(
      isSelected
        ? selectedLocations.filter((loc) => loc !== id)
        : [...selectedLocations, id]
    );
  };

  const handleSave = () => {
    onSelect(selectedLocations);
  };

  if (addLocation) {
    return (
      <AddLocation
        onClose={() => {
          setAddLocation(false);
        }}
      />
    );
  }

  if (isLoading)
    return (
      <div className="flex h-80 w-full items-center justify-center">
        <div className="loader" />
      </div>
    );

  return (
    <>
      <div>
        <div className="max-h-[50vh] space-y-4 overflow-auto">
          {locations.map(({ id, address, city: { name } }) => (
            <CheckRadio
              key={id}
              id={id}
              text={name}
              additionalText={address}
              isChecked={selectedLocations.includes(id)}
              onChange={handleLocationClick}
            />
          ))}
        </div>

        {locations.length === 0 && (
          <div className="py-4 text-center text-white text-opacity-60">
            Nie znaleziono lokalizacji
          </div>
        )}
      </div>

      <div className="mt-4">
        <Button
          borderRadius="rounded"
          fullWidth
          padding="lg"
          onClick={() => setAddLocation(true)}
          variant="secondary"
        >
          Nowy adres
        </Button>
      </div>

      <div className="mt-1">
        <Button
          borderRadius="rounded"
          fullWidth
          padding="lg"
          onClick={handleSave}
          disabled={!selectedLocations.length}
        >
          Zapisz
        </Button>
      </div>
    </>
  );
}
