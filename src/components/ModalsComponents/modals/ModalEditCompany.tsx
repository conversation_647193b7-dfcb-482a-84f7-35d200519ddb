import React, { Dispatch, SetStateAction, useState } from 'react';

import { Button } from '@app/elements/Buttons';
import {
  TextField,
  TextFieldTextAreaClickable,
  TextFieldWithIcon,
} from '@app/elements/TextFields';

import { useModal } from '@components/ModalsComponents/context/ModalContext';

import { CompanySizeSelector } from './CompanySizeSelector';
import { ModalAboutUs } from './ModalAboutUs';
import { CompanyFormData } from './types';

export function ModalEditCompany() {
  const {
    openNestedModal,
    isNestedModalOpen,
    currentNestedModalType,
    closeNestedModal,
    handleVariableModalSave,
    modalDefaultValue,
  } = useModal();

  const companyExists = modalDefaultValue !== null;

  const initialFormData: CompanyFormData =
    typeof modalDefaultValue === 'object' && companyExists
      ? (modalDefaultValue as CompanyFormData)
      : {
          name: '',
        };

  const [formData, setFormData] = useState(initialFormData);

  const getFieldUpdater =
    (field: keyof CompanyFormData): Dispatch<SetStateAction<string>> =>
    (value) => {
      const newValue =
        typeof value === 'function' ? value(formData[field] || '') : value;
      setFormData((prev) => ({ ...prev, [field]: newValue }));
    };

  const handleSaveData = () => {
    if (handleVariableModalSave) {
      handleVariableModalSave(formData);
    }
  };

  if (isNestedModalOpen && currentNestedModalType === 'company_size') {
    return (
      <CompanySizeSelector
        currentSize={formData.size || ''}
        onSizeSelect={getFieldUpdater('size')}
        onClose={closeNestedModal}
      />
    );
  }

  if (isNestedModalOpen && currentNestedModalType === 'about_us') {
    return (
      <ModalAboutUs
        defaultValue={formData.aboutCompany}
        onSave={(newValue: string) => {
          getFieldUpdater('aboutCompany')(newValue);
          closeNestedModal();
        }}
      />
    );
  }

  return (
    <>
      <TextField
        title="Nazwa firmy"
        input={formData.name}
        setInput={getFieldUpdater('name')}
        placeholder="Podaj nazwę firmy"
        fullWidth
      />
      <TextField
        title="Strona internetowa"
        input={formData.website}
        setInput={getFieldUpdater('website')}
        placeholder="Podaj link do strony internetowej"
        fullWidth
      />
      <TextFieldWithIcon
        title="Rozmiar firmy"
        input={formData.size}
        setInput={getFieldUpdater('size')}
        icon="editPen"
        iconSize="sm"
        fullWidth
        placeholder="Wybierz liczbę pracowników"
        onClick={() => openNestedModal('company_size')}
      />
      {companyExists && (
        <TextFieldTextAreaClickable
          title="O nas"
          value={formData.aboutCompany}
          placeholder="Wprowadź opis firmy"
          fullWidth
          icon="editPen"
          iconSize="sm"
          onClick={() => openNestedModal('about_us')}
        />
      )}

      <Button onClick={handleSaveData} className="mx-auto px-10">
        {companyExists ? 'Zapisz Dane' : 'Stwórz firmę'}
      </Button>
    </>
  );
}
