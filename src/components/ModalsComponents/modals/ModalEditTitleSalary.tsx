import { useState, SetStateAction, useEffect } from 'react';

import { Button } from '@app/elements/Buttons';
import { TextField, TextFieldWithIcon } from '@app/elements/TextFields';
import { Text } from '@app/elements/Texts';

import { useModal } from '@components/ModalsComponents/context/ModalContext';
import PriceRange from '@components/PriceRange/PriceRange';

import { CategorySelection } from './CategorySelector';
import { ModalCategory } from './ModalCategory';

export function ModalEditTitleSalary() {
  const {
    handleVariableModalSave,
    modalDefaultValue,
    openNestedModal,
    isNestedModalOpen,
    currentNestedModalType,
    closeNestedModal,
  } = useModal();

  interface TitleSalaryData {
    title: string;
    minSalary: string;
    maxSalary: string;
    currency: string;
    category?: CategorySelection | null;
  }

  const defaultData =
    typeof modalDefaultValue === 'object' && modalDefaultValue
      ? (modalDefaultValue as TitleSalaryData)
      : {
          title: '',
          minSalary: '',
          maxSalary: '',
          currency: 'PLN',
          category: null,
        };

  const [formData, setFormData] = useState({
    title: defaultData.title || '',
    minSalary: defaultData.minSalary || '',
    maxSalary: defaultData.maxSalary || '',
    currency: defaultData.currency || 'PLN',
    category: defaultData.category || null,
  });

  const [validationError, setValidationError] = useState('');

  const setSalaryFrom = (value: SetStateAction<string>) => {
    handleChange('minSalary', value);
  };

  const setSalaryTo = (value: SetStateAction<string>) => {
    handleChange('maxSalary', value);
  };

  const getCategoryDisplayText = (): string => {
    if (formData.category?.subcategory) {
      return `${formData.category.category.name} - ${formData.category.subcategory.name}`;
    }
    if (formData.category?.category && !formData.category.subcategory) {
      return `Wszystko w "${formData.category.category.name}"`;
    }
    return '';
  };

  useEffect(() => {
    const hasMinSalary = formData.minSalary.trim() !== '';
    const hasMaxSalary = formData.maxSalary.trim() !== '';

    const minSalaryNum = hasMinSalary ? parseFloat(formData.minSalary) : 0;
    const maxSalaryNum = hasMaxSalary ? parseFloat(formData.maxSalary) : 0;

    if ((hasMinSalary && !hasMaxSalary) || (!hasMinSalary && hasMaxSalary)) {
      setValidationError('Podaj zakres lub nie podawaj go wcale');
    } else if (hasMinSalary && minSalaryNum <= 0) {
      setValidationError('Minimalna wartość musi być większa od 0');
    } else if (hasMaxSalary && maxSalaryNum <= 0) {
      setValidationError('Maksymalna wartość musi być większa od 0');
    } else if (hasMinSalary && hasMaxSalary && minSalaryNum >= maxSalaryNum) {
      setValidationError('Wartość minimalna musi być mniejsza od maksymalnej');
    } else {
      setValidationError('');
    }
  }, [
    formData.minSalary,
    formData.maxSalary,
    formData.category,
    formData.title,
  ]);

  const handleChange = (field: string, value: SetStateAction<string>) => {
    if (typeof value === 'function') {
      setFormData((prev) => {
        const currentValue = prev[field as keyof typeof prev] as string;
        const newValue = (value as (prevState: string) => string)(currentValue);
        return {
          ...prev,
          [field]: newValue,
        };
      });
    } else {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const handleSave = () => {
    if (validationError) {
      return;
    }

    if (!formData.title.trim()) {
      setValidationError('Podaj nazwę stanowiska');
      return;
    }

    if (!formData.category?.subcategory) {
      setValidationError('Wybierz konkretną specjalizację');
      return;
    }

    if (formData.minSalary.trim() === '' && formData.maxSalary.trim() === '') {
      handleVariableModalSave?.({
        ...formData,
        minSalary: '',
        maxSalary: '',
      });
      return;
    }

    const minSalaryNum = parseFloat(formData.minSalary);
    const maxSalaryNum = parseFloat(formData.maxSalary);

    if (
      minSalaryNum <= 0 ||
      maxSalaryNum <= 0 ||
      minSalaryNum >= maxSalaryNum
    ) {
      return;
    }

    handleVariableModalSave?.(formData);
  };

  if (isNestedModalOpen && currentNestedModalType === 'category') {
    return (
      <ModalCategory
        defaultValue={formData.category}
        showAllCategoryOption={false}
        onSave={(newCategory: CategorySelection | null) => {
          setFormData((prev) => ({
            ...prev,
            category: newCategory,
          }));
          closeNestedModal();
        }}
      />
    );
  }

  return (
    <>
      <TextField
        title="Nazwa stanowiska"
        input={formData.title}
        setInput={(value) => handleChange('title', value)}
        placeholder="Podaj nazwę stanowiska"
        borderRadius="rounded"
      />

      <TextFieldWithIcon
        title="Kategoria"
        input={getCategoryDisplayText()}
        setInput={() => {}}
        icon="editPen"
        iconSize="sm"
        fullWidth
        placeholder="Wybierz specjalizację"
        onClick={() => openNestedModal('category')}
      />

      <PriceRange
        salaryFrom={formData.minSalary}
        salaryTo={formData.maxSalary}
        setSalaryFrom={setSalaryFrom}
        setSalaryTo={setSalaryTo}
      />

      {validationError && (
        <div className="flex items-center gap-2">
          <span className="size-2 rounded-full bg-red-500" />
          <Text fontSize="sm" className="text-red-500">
            {validationError}
          </Text>
        </div>
      )}

      <Button
        borderRadius="rounded"
        fullWidth
        onClick={handleSave}
        disabled={!!validationError}
      >
        Zapisz zmiany
      </Button>
    </>
  );
}
