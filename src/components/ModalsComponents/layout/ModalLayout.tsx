'use client';

import React, { useEffect } from 'react';

import { createPortal } from 'react-dom';

import { borderRadiusStyles } from '@app/elements/_shared';
import { Button, ButtonAsIcon } from '@app/elements/Buttons';

import {
  ModalTypeNaming,
  possibleModals,
  useModal,
} from '@components/ModalsComponents/context/ModalContext';
import { ModalTypeProps } from '@components/ModalsComponents/context/types';

import {
  ModalEditCompany,
  LocationSelector,
  ModalEditTitleSalary,
  CompanySizeSelector,
  ModalExpectationsRequirements,
  ModalTypeOfContract,
  ModalAboutUs,
  ModalFilter,
  ModalRegister,
  ModalForgotPassword,
  ModalLogin,
  ModalChangePassword,
} from '../modals';
import { ModalMenu } from '../modals/ModalMenu';

// This function decides what modal to show
function ModalController({
  currentModalType,
}: {
  currentModalType: ModalTypeProps;
}) {
  const { modalDefaultValue, closeModal, handleVariableModalSave } = useModal();

  // Check ModalContext.tsx for all options of modalTypes
  if (currentModalType === 'filter') {
    return <ModalFilter />;
  }

  if (currentModalType === 'login') {
    return <ModalLogin />;
  }

  if (currentModalType === 'forgot_password') {
    return <ModalForgotPassword />;
  }

  if (currentModalType === 'change_password') {
    return <ModalChangePassword />;
  }

  if (currentModalType === 'signin') {
    return <ModalRegister />;
  }

  if (currentModalType === 'select_type_of_contract') {
    return <ModalTypeOfContract />;
  }

  if (currentModalType === 'about_us') {
    return <ModalAboutUs />;
  }

  if (currentModalType === 'company_edit') {
    return <ModalEditCompany />;
  }

  if (currentModalType === 'location_selector') {
    return (
      <LocationSelector
        selectedLocations={modalDefaultValue as unknown as string[]}
        onSelect={(location) => {
          if (handleVariableModalSave) {
            handleVariableModalSave(location);
          }
        }}
      />
    );
  }

  if (currentModalType === 'edit_title_salary') {
    return <ModalEditTitleSalary />;
  }

  if (currentModalType === 'company_size_selector') {
    return (
      <CompanySizeSelector
        currentSize={
          typeof modalDefaultValue === 'string' ? modalDefaultValue : ''
        }
        onSizeSelect={(size) => {
          if (handleVariableModalSave) {
            handleVariableModalSave(size);
          }
        }}
        onClose={closeModal}
      />
    );
  }

  if (
    currentModalType === 'expectations' ||
    currentModalType === 'offer' ||
    currentModalType === 'daily_tasks'
  ) {
    return <ModalExpectationsRequirements />;
  }

  if (currentModalType === 'menu') {
    return <ModalMenu />;
  }
}

// This is just our main wrapper for all modals
export function ModalLayout() {
  const {
    isModalOpen,
    openModal,
    currentModalType,
    closeModal,
    switchBetweenModals,
    setCurrentModalType,
    backToModal,
    setBackToModal,
    isNestedModalOpen,
    closeNestedModal,
  } = useModal(); // Dodaj setModal, aby zamknąć modal

  useEffect(() => {
    if (isModalOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
  }, [isModalOpen]);

  const isValidModal =
    isModalOpen &&
    currentModalType &&
    possibleModals.includes(currentModalType);

  if (isValidModal === false) return null;

  // Close modal when we click on background
  const handleCloseModal = () => {
    if (isNestedModalOpen) {
      closeNestedModal();
    } else {
      closeModal();
    }
  };

  // Escape key will close modal
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    // Escape only works when we don't have nested modal open at that time
    if (e.key === 'Escape' && isNestedModalOpen === false) {
      closeModal();
    }

    // If nested modal is open, first close nested
    if (e.key === 'Escape' && isNestedModalOpen === true) {
      closeNestedModal();
    }
  };

  const handleModalPrevious = () => {
    setCurrentModalType(backToModal);
    setBackToModal(null);
  };

  return createPortal(
    <div
      className={`fixed left-0 top-0 z-20 flex h-screen w-screen cursor-default flex-col items-center justify-center gap-4 bg-[#70707050] p-4 backdrop-blur-xl ${
        isValidModal ? 'animate-fadeIn' : 'hidden'
      }`}
      // onClick={handleCloseModal}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label="Close modal"
    >
      {/* Close button  */}
      <div className="flex w-full max-w-modalMax justify-center gap-4">
        <ButtonAsIcon
          buttonIcon="close"
          iconSize="xl"
          padding="sm"
          onClick={handleCloseModal}
        />
      </div>

      {/* Switch between modals container  */}
      {switchBetweenModals && switchBetweenModals.length > 0 && (
        <div
          role="none"
          // eslint-disable-next-line tailwindcss/no-unnecessary-arbitrary-value
          className={`flex w-full max-w-modalMax gap-1 bg-[#FFFFFF10] p-1 ${borderRadiusStyles.rounded} overflow-hidden`}
          onClick={(e) => e.stopPropagation()}
        >
          {/* // Back to previous modal if added  */}
          {backToModal && (
            <ButtonAsIcon
              buttonIcon="arrowLeft"
              iconSize="md"
              onClick={handleModalPrevious}
              className="min-w-[56px]"
            />
          )}

          {switchBetweenModals.map((ele, idx) => {
            return (
              <Button
                className={`font-normal ${currentModalType === ele ? 'cursor-default bg-btn-primary-hover-bg' : 'bg-transparent'}`}
                key={`option-${ele}-${idx}`}
                fullWidth
                onClick={
                  ele !== currentModalType ? () => openModal(ele) : undefined
                }
              >
                {ModalTypeNaming[ele]}
              </Button>
            );
          })}
        </div>
      )}

      {/* Modal Content */}
      <div
        onKeyDown={handleKeyDown}
        role="none"
        // eslint-disable-next-line tailwindcss/no-unnecessary-arbitrary-value
        className={`min-h-[100px] w-full max-w-modalMax ${borderRadiusStyles.extra} flex flex-col gap-4 bg-[#ffffff20] p-4 shadow-sm`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Render proper modal  */}
        <ModalController currentModalType={currentModalType} />
      </div>
    </div>,
    document.body
  );
}
