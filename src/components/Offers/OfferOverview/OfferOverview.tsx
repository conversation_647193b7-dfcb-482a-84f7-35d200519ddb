'use client';

import React, { useState } from 'react';

import { useTranslations } from 'next-intl';

import { Button } from '@app/elements/Buttons';
import {
  TextFieldReadOnly,
  TextFieldTextAreaClickable,
  TextFieldTextAreaReadOnly,
  TextFieldWithIcon,
} from '@app/elements/TextFields';
import { Text } from '@app/elements/Texts';

import { EditPenIcon, FavoriteIcon } from '@components/icons';
import { typeOfContractOptions } from '@components/ModalsComponents';
import {
  ApplicationSuccesModal,
  ApplicationModal,
} from '@components/ModalsComponents/modals';
import { OfferImage } from '@components/Offers/SingleOffer/SingleOffer';
import { OfferOverviewSkeleton } from '@components/ui/Skeleton/skeletons/OfferOverviewSkeleton';

import { useClickOutside } from '@hooks/useClickOutside';
import { useMediaQuery } from '@hooks/useMediaQuery';

import { OfferOverviewProps } from './types';
import { useJobOfferEdit } from '../hooks/useJobOfferEdit';

export default function OfferOverview({
  offer,
  isEditable = false,
  onSaveChanges,
  showButton = true,
}: OfferOverviewProps) {
  const [addToFavorite, setAddToFavorite] = useState<boolean>(false);
  const [openDropDown, setOpenDropdown] = useState<boolean>(false);
  const [isApplicationModalOpened, setIsApplicationModalOpened] =
    useState<boolean>(false);
  const [isApplicationSuccessModalOpened, setIsApplicationSuccessModalOpened] =
    useState<boolean>(false);
  const ref = useClickOutside(() => setOpenDropdown(false));

  const t = useTranslations('Offer');
  const isMobile = useMediaQuery('(max-width: 1024px)');

  const initialOfferData = React.useMemo(() => {
    if (!offer) return {};

    return {
      title: offer.position,
      location:
        offer.locations.map((loc) => {
          if (!loc) return '';

          return `${loc.city.name}${loc.address ? `, ${loc.address}` : ''}`;
        }) || [],
      minSalary: offer.minSalary,
      maxSalary: offer.maxSalary,
      currency: offer.currency,
      contractType: offer.contractType,
      companySize: offer.assignedTo.size,
      aboutCompany: offer.aboutCompany,
      ourExpectations: offer.ourExpectations,
      yourDailyTasks: offer.yourDailyTasks,
      whatWeOffers: offer.whatWeOffers,
    };
  }, [offer]);

  // Nazwa kategorii dla trybu edycji
  const categoryName = offer?.category?.name
    ? `Kategoria: ${offer.category.name}`
    : null;

  const {
    offerData,
    formatArrayToString,
    handleLocationClick,
    handleContractTypeClick,
    handleAboutUsClick,
    handleOurExpectationsClick,
    handleOurOfferClick,
    handleTitleSalaryClick,
    handleCompanySizeClick,
  } = useJobOfferEdit(initialOfferData);

  console.log(offerData);

  const handleAddToFavorite = () => {
    setAddToFavorite((prev) => !prev);
  };

  const toggleDropdown = () => {
    setOpenDropdown((prev) => !prev);
  };

  const dropDownIcon = openDropDown ? 'arrowDropUp' : 'arrowDropDown';

  const salaryText = () => {
    if (!offer || !offer.minSalary || !offer.maxSalary) {
      return t('noSalaryProvided');
    }

    const salaryRange = `${offer?.minSalary?.replace(/(.)(?=(\d{3})+$)/g, '$1 ')} - 
            ${offer?.maxSalary?.replace(/(.)(?=(\d{3})+$)/g, '$1 ')}
            ${offer?.currency}`;

    if (!isMobile) {
      return (
        <div>
          <Text fontSize="md" isSpan className="mr-1">
            {salaryRange}
          </Text>
          <Text
            fontSize="xs"
            className="text-nowrap text-tf-text-title text-opacity-50"
            isSpan
          >
            / {t('month')}
          </Text>
        </div>
      );
    }

    return (
      <Text fontSize="md" isSpan className="mr-1">
        {salaryRange}
      </Text>
    );
  };

  if (!offer) {
    return <OfferOverviewSkeleton />;
  }

  const renderHeader = () => (
    <div className="flex items-center gap-3">
      <OfferImage url={offer.assignedTo.logo} />
      <div>
        <Text fontSize="md" className="text-white text-opacity-50">
          {offer.assignedTo.name}
        </Text>
        <div className="flex items-center gap-3">
          <Text fontSize="lg" className="font-semibold text-white">
            {isEditable ? offerData?.title : offer.position}
          </Text>
          {isEditable && (
            <EditPenIcon size={24} onClick={handleTitleSalaryClick} />
          )}
        </div>
        {isEditable && categoryName && (
          <Text fontSize="xs" className="font-semibold text-white">
            {categoryName}
          </Text>
        )}
      </div>

      {!isEditable && (
        <FavoriteIcon
          onClick={handleAddToFavorite}
          size="xl"
          className={`ml-auto mr-2 cursor-pointer ${
            addToFavorite ? 'animate-pulseShadow rounded-full' : ''
          }`}
          style={{
            color: addToFavorite ? '#E42B83' : '#ffffff',
            opacity: addToFavorite ? 1 : 0.4,
          }}
        />
      )}
    </div>
  );

  // Tryb edycji
  const renderEditMode = () => (
    <>
      {renderHeader()}

      <div className="grid grid-cols-2 gap-4">
        <TextFieldWithIcon
          title={t('salary')}
          input={`${offerData?.minSalary} - ${offerData?.maxSalary} ${offerData?.currency}`}
          icon="editPen"
          textSize="sm"
          iconSize="sm"
          readOnly
          fullWidth
          onClick={handleTitleSalaryClick}
        />
        <TextFieldWithIcon
          title={t('location')}
          input={offerData?.locations}
          icon="editPen"
          textSize="sm"
          iconSize="sm"
          readOnly
          fullWidth
          isLocationArray
          onClick={handleLocationClick}
        />
        <TextFieldWithIcon
          title={t('companySize')}
          // icon="editPen"
          input={offerData?.companySize}
          textSize="sm"
          iconSize="sm"
          readOnly
          fullWidth
          // onClick={handleCompanySizeClick}
        />
        <TextFieldWithIcon
          title={t('contract')}
          input={
            offerData?.contractType
              ? typeOfContractOptions[offerData.contractType]
              : '-'
          }
          icon="editPen"
          textSize="sm"
          iconSize="sm"
          readOnly
          fullWidth
          onClick={handleContractTypeClick}
        />
      </div>

      <TextFieldTextAreaClickable
        title={t('aboutCompany')}
        value={offerData.aboutCompany || ''}
        fullWidth
        icon="editPen"
        textSize="sm"
        iconSize="sm"
        onClick={handleAboutUsClick}
      />
      <TextFieldTextAreaClickable
        title={t('expectations')}
        value={
          offerData?.ourExpectations && offerData?.ourExpectations.length > 0
            ? formatArrayToString(offerData.ourExpectations)
            : ''
        }
        fullWidth
        icon="editPen"
        textSize="sm"
        iconSize="sm"
        onClick={handleOurExpectationsClick}
      />
      <TextFieldTextAreaClickable
        title={t('dailyTasks')}
        value={
          offerData?.yourDailyTasks && offerData?.yourDailyTasks.length > 0
            ? formatArrayToString(offerData.yourDailyTasks)
            : ''
        }
        fullWidth
        icon="editPen"
        textSize="sm"
        iconSize="sm"
        onClick={handleOurOfferClick}
      />

      <div className="flex justify-center">
        <Button
          onClick={() => onSaveChanges && onSaveChanges(offerData)}
          fontSize="md"
          fullWidth
          className="max-w-60"
        >
          Zapisz zmiany
        </Button>
      </div>
    </>
  );

  // Tryb podglądu
  const renderViewMode = () => (
    <>
      {renderHeader()}

      <div className="grid grid-cols-2 gap-3">
        <TextFieldReadOnly
          title={t('salary')}
          titleValue={salaryText()}
          fullWidth
        />
        <TextFieldReadOnly
          title={t('location')}
          titleValue={offer.locations?.map((loc) => {
            if (!loc) return '';

            return `${loc.city.name}${loc.address ? `, ${loc.address}` : ''}`;
          })}
          fullWidth
          isDropdownOpen={openDropDown}
          toggleDropdown={toggleDropdown}
          ref={ref}
          icon={dropDownIcon}
          className="cursor-pointer"
        />
        <TextFieldReadOnly
          title={t('companySize')}
          titleValue={offer.assignedTo?.size}
          fullWidth
        />
        <TextFieldReadOnly
          title={t('contract')}
          titleValue={
            offerData?.contractType
              ? typeOfContractOptions[offerData.contractType]
              : '-'
          }
          fullWidth
        />
      </div>

      <TextFieldTextAreaReadOnly
        title={t('aboutCompany')}
        titleValue={offer.aboutCompany}
        fullWidth
      />

      {offer.ourExpectations?.length > 0 && (
        <TextFieldTextAreaReadOnly
          title={t('expectations')}
          titleValue={offer.ourExpectations}
          fullWidth
        />
      )}
      {offer.yourDailyTasks?.length > 0 && (
        <TextFieldTextAreaReadOnly
          title={t('dailyTasks')}
          titleValue={offer.yourDailyTasks}
          fullWidth
        />
      )}
      {offer.whatWeOffers?.length > 0 && (
        <TextFieldTextAreaReadOnly
          title={t('whatWeOffer')}
          titleValue={offer.whatWeOffers}
          fullWidth
        />
      )}

      {showButton ? (
        <div className="flex justify-center">
          <Button
            onClick={() => setIsApplicationModalOpened(true)}
            fontSize="md"
            fullWidth
            className="max-w-60"
          >
            {t('buttonText')}
          </Button>
        </div>
      ) : null}
    </>
  );

  return (
    <>
      <div className="flex-1 rounded-[30px] bg-layout-box-bg backdrop-blur-default">
        <div className="flex flex-col justify-center gap-4 overflow-visible p-4">
          {isEditable ? renderEditMode() : renderViewMode()}
        </div>
      </div>
      {isApplicationModalOpened && (
        <ApplicationModal
          closeModal={() => setIsApplicationModalOpened(false)}
          onSuccess={() => {
            setIsApplicationModalOpened(false);
            setIsApplicationSuccessModalOpened(true);
          }}
          offer={offer}
        />
      )}
      {isApplicationSuccessModalOpened && (
        <ApplicationSuccesModal
          closeModal={() => setIsApplicationSuccessModalOpened(false)}
        />
      )}
    </>
  );
}
