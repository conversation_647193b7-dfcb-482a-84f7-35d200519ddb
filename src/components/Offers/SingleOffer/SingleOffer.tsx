'use client';

import { useMemo } from 'react';

import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

import { useTranslations } from 'next-intl';

import { ImageComponent } from '@app/elements/ImageComponent';
import { Text } from '@app/elements/Texts';

import { MappedOffer } from '@components/Company/types';

import { OfferProps } from '../types';
import { SingleOfferProps, OfferImageProps } from './types';
import { WalletIcon, LocationIcon } from '../../icons/IconWrapper';

// tutaj podmieniłem div na Image i zostawiłem zakomentowaną drugą wersje.
// Jeszcze te Image są do dopracowania bo idealnie by było zeby Image zajmował całego diva i jednocześnie
// się nie przycinał po bokach
// (choź niektóre fotki się dobrze pozycjonują - moze nadanie głwonych rozmiarów w div? )
// }

export function OfferImage({ url }: OfferImageProps) {
  return (
    <div className="relative h-[64px] w-[100px] overflow-hidden rounded-[20px] tablet:h-[70px] tablet:w-[124px]">
      <ImageComponent
        src={url}
        alt="Company logo"
        fill
        isLogo
        objectFit="contain"
        priority
      />
    </div>
  );
}

export function SingleOffer({ offer, active }: SingleOfferProps) {
  const searchParams = useSearchParams();
  const t = useTranslations('Company');

  // Sprawdzamy, czy mamy do czynienia z MappedOffer czy OfferProps
  const isMappedOffer = (o: OfferProps | MappedOffer): o is MappedOffer => {
    return 'CompanyName' in o;
  };

  // Pobieramy odpowiednie wartości w zależności od typu
  const getId = () => {
    if (isMappedOffer(offer)) {
      return ''; // MappedOffer nie ma id, wieć obługujemy ten przypadek
    }
    return offer.id;
  };

  const getCompanyName = () => {
    if (isMappedOffer(offer)) {
      return offer.CompanyName;
    }
    return offer.assignedTo.name;
  };

  const getPosition = () => {
    if (isMappedOffer(offer)) {
      return offer.JobPosition;
    }
    return offer.position;
  };

  const getSalaryRange = () => {
    if (isMappedOffer(offer)) {
      return offer.SalaryRange;
    }
    return `${offer.minSalary} - ${offer.maxSalary}`;
  };

  const locationsLabel = useMemo(() => {
    if (isMappedOffer(offer)) {
      return `${offer.OfferCity}, ${offer.OfferCountry}`;
    }

    const locationsAmount = offer.locations?.length;
    if (!locationsAmount) return null;

    const firstLocation = offer.locations[0];
    const locationString = `${firstLocation.city.name}${firstLocation.address ? `, ${firstLocation.address}` : ''}`;

    if (locationsAmount === 1) return locationString;

    if (locationsAmount === 2) {
      return `${locationString} +1 ${t('location')}`;
    }

    return `${locationString} +${locationsAmount - 1} ${t('locations')}`;
  }, [offer, t]);

  const getLogoUrl = () => {
    if (isMappedOffer(offer)) {
      return offer.OfferImage;
    }
    return offer.assignedTo.logo;
  };

  const getExpirationInfo = () => {
    if (!isMappedOffer(offer) || !offer.CreatedDate) return null;

    const createdDate = new Date(offer.CreatedDate);
    const expirationDate = new Date(createdDate);
    expirationDate.setMonth(expirationDate.getMonth() + 1); // expire after 1 month

    const isExpired = new Date() > expirationDate;

    const day = expirationDate.getDate().toString().padStart(2, '0');
    const month = (expirationDate.getMonth() + 1).toString().padStart(2, '0');
    const year = expirationDate.getFullYear();

    return {
      isExpired,
      dateFormatted: `${day}.${month}.${year}`,
    };
  };

  const getOfferUrl = () => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set('activeOffer', getId().toString());
    return `?${newParams.toString()}`;
  };

  return (
    <Link
      className={`flex min-h-max min-w-[350px] cursor-pointer items-center gap-2 overflow-hidden rounded-[30px] ${active ? 'bg-layout-box-bg-active' : 'bg-layout-box-bg'} p-3 backdrop-blur-default duration-default ${active ? '' : 'hover:bg-layout-box-bg-hover'}`}
      href={getOfferUrl()}
      shallow
      id={`offer-${getId()}`}
    >
      <div>
        <OfferImage url={getLogoUrl()} />
      </div>

      <div>
        <div className="flex items-center gap-2">
          <Text> {getCompanyName()} </Text>
          {isMappedOffer(offer) && getExpirationInfo() && (
            <div className="flex items-center gap-1 rounded-3xl bg-[#ffffff20] px-2 ">
              <span
                className={`block size-2 rounded-full ${getExpirationInfo()?.isExpired ? 'bg-[#FF4242]' : 'bg-[#FFDD00]'}`}
              />
              <Text fontSize="xs" className="text-opacity-70">
                {getExpirationInfo()?.isExpired ? 'Wygasła' : 'Wygaśnie'}
                {getExpirationInfo()?.dateFormatted}
              </Text>
            </div>
          )}
        </div>
        <Text fontSize="md" variant="white" fontWeight="medium">
          {getPosition()}
        </Text>

        <div className="flex gap-2">
          <div className="flex gap-1 whitespace-nowrap">
            <WalletIcon size={16} />
            <Text>{getSalaryRange()}</Text>
          </div>

          {locationsLabel && (
            <div className="grid grid-cols-[auto,1fr] justify-center gap-0.5">
              <LocationIcon size={16} />
              <Text className="truncate">{locationsLabel}</Text>
            </div>
          )}
        </div>
      </div>
    </Link>
  );
}
