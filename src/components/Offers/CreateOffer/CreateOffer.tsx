'use client';

import React, { useCallback, useEffect } from 'react';

import { useRouter } from 'next/navigation';

import { useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';

import { Button } from '@app/elements/Buttons';
import { ImageComponent } from '@app/elements/ImageComponent';
import {
  TextFieldTextAreaClickable,
  TextFieldWithIcon,
} from '@app/elements/TextFields';
import { Text } from '@app/elements/Texts';

import { EditPenIcon } from '@components/icons';
import { typeOfContractOptions } from '@components/ModalsComponents';

import { useCreateOffer } from '@actions/hooks/useCreateOffer';
import { useLocations } from '@actions/hooks/useLocations';
import { toast, ToastIconVariant } from '@hooks/use-toast';

import { CreateOfferProps } from './types';
import { useJobOfferEdit } from '../hooks/useJobOfferEdit';

export default function CreateOffer({ companyData }: CreateOfferProps) {
  const t = useTranslations('Offer');
  const createOffer = useCreateOffer();
  const router = useRouter();
  const queryClient = useQueryClient();
  const locationsData = useLocations();
  const allLocations = locationsData?.data?.data || [];

  const {
    offerData,
    formatArrayToString,
    handleLocationClick,
    handleContractTypeClick,
    handleAboutUsClick,
    handleOurExpectationsClick,
    handleOurOfferClick,
    handleTitleSalaryClick,
    handleYourDailyTasksClick,
  } = useJobOfferEdit({ aboutCompany: companyData.aboutCompany });

  useEffect(() => {
    handleTitleSalaryClick();
  }, []);

  const publish = useCallback(async () => {
    const now = new Date();
    const endDate = new Date();
    endDate.setMonth(now.getMonth() + 1);

    let categoryIdsToSend: number[] = [];

    if (offerData?.category?.subcategory) {
      // Wybrana konkretna subkategoria
      categoryIdsToSend = [offerData.category.subcategory.id];
    } else if (offerData?.category?.category) {
      // Wybrana cała kategoria - wyślij wszystkie subkategorie
      categoryIdsToSend =
        offerData.category.category.subcategories?.map((sub) => sub.id) || [];
    }

    if (categoryIdsToSend.length === 0) {
      toast({
        iconVariant: ToastIconVariant.ERROR,
        description: 'Musisz wybrać specjalizację',
      });
      return;
    }

    try {
      const response = await createOffer({
        position: offerData.title,
        category: categoryIdsToSend[0],
        minSalary: offerData.minSalary
          ? Number(offerData.minSalary)
          : undefined,
        maxSalary: offerData.maxSalary
          ? Number(offerData.maxSalary)
          : undefined,
        locations: offerData.locations,
        currency: offerData.currency || 'PLN', // TODO: brakuje UI
        contractType: offerData.contractType,
        startDate: now,
        endDate: endDate,
        aboutCompany: offerData.aboutCompany,
        yourDailyTasks: offerData.yourDailyTasks,
        ourExpectations: offerData.ourExpectations,
        whatWeOffers: offerData.whatWeOffers,
        assignedTo: companyData.company.id,
      });

      await queryClient.invalidateQueries({
        queryKey: ['getCompany', { id: String(companyData.company.id) }],
      });

      await queryClient.invalidateQueries({
        queryKey: ['get-job-offers'],
      });

      router.push(`/?activeOffer=${response.data.id}`);
    } catch (error) {
      console.error('❌ Error creating offer:', error);
    }
  }, [createOffer, offerData, companyData, router, queryClient]);

  const {
    title,
    locations,
    contractType,
    minSalary,
    maxSalary,
    // currency,
    aboutCompany,
    ourExpectations,
    whatWeOffers,
    yourDailyTasks,
    category,
  } = offerData;

  const categoryName = `Kategoria: ${category?.subcategory?.name}`;

  return (
    <div className="flex flex-col gap-4 overflow-auto rounded-2xl bg-layout-box-bg p-4 backdrop-blur-default laptop:w-3/5">
      <div className="flex items-center gap-3">
        <div className="relative h-[75px] w-[120px] shrink-0 mobile:w-[140px] tablet:w-[160px]">
          <ImageComponent
            src={companyData.company.logo}
            alt="Company Logo"
            fill
            className="rounded-[20px] border-gray-300"
            isLogo
            objectFit="contain"
            priority={false}
          />
        </div>
        <div className="flex flex-col ">
          <div>
            <Text fontSize="sm" className="text-white text-opacity-50">
              {companyData.company.name}
            </Text>
          </div>

          <div className="flex items-center gap-3">
            <Text fontSize="md" className="font-semibold text-white">
              {title || t('addPosition')}
            </Text>

            <EditPenIcon
              onClick={handleTitleSalaryClick}
              size={24}
              className="cursor-pointer"
            />
          </div>
          {category?.subcategory && (
            <Text fontSize="xs" className="font-semibold text-white">
              {categoryName}
            </Text>
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <TextFieldWithIcon
          title={t('salary')}
          // TODO: salary parser
          input={
            minSalary && maxSalary ? `${minSalary} - ${maxSalary} PLN` : ''
          }
          placeholder={t('addSalary')}
          icon="editPen"
          textSize="sm"
          iconSize="sm"
          readOnly
          fullWidth
          onClick={handleTitleSalaryClick}
        />

        <TextFieldWithIcon
          title={t('location')}
          input={
            locations
              ?.map((id) => allLocations?.find((loc) => loc.id === id))
              .map((loc) => {
                if (!loc) return '';
                return `${loc.city.name}${loc.address ? `, ${loc.address}` : ''}`;
              }) || ''
          }
          placeholder={t('addLocation')}
          icon="editPen"
          textSize="sm"
          iconSize="sm"
          readOnly
          fullWidth
          isLocationArray
          onClick={handleLocationClick}
        />

        <TextFieldWithIcon
          title={t('companySize')}
          // icon="editPen"
          input={companyData.company.size}
          placeholder={t('add')}
          textSize="sm"
          iconSize="sm"
          readOnly
          fullWidth
          // onClick={handleCompanySizeClick}
        />

        <TextFieldWithIcon
          title={t('contract')}
          input={contractType ? typeOfContractOptions[contractType] : ''}
          placeholder={t('addContract')}
          icon="editPen"
          readOnly
          fullWidth
          textSize="sm"
          iconSize="sm"
          onClick={handleContractTypeClick}
        />
      </div>

      <TextFieldTextAreaClickable
        title={t('aboutCompany')}
        value={aboutCompany || ''}
        placeholder={t('addAboutCompany')}
        fullWidth
        icon="editPen"
        textSize="sm"
        iconSize="sm"
        onClick={handleAboutUsClick}
      />

      <TextFieldTextAreaClickable
        title={t('expectations')}
        value={
          ourExpectations && ourExpectations.length > 0
            ? formatArrayToString(ourExpectations)
            : ''
        }
        placeholder={t('addExpectations')}
        fullWidth
        icon="editPen"
        textSize="sm"
        iconSize="sm"
        shouldResize
        onClick={handleOurExpectationsClick}
      />

      <TextFieldTextAreaClickable
        title={t('dailyTasks')}
        value={
          yourDailyTasks && yourDailyTasks.length > 0
            ? formatArrayToString(yourDailyTasks)
            : ''
        }
        placeholder={t('addDailyTasks')}
        fullWidth
        icon="editPen"
        textSize="sm"
        iconSize="sm"
        shouldResize
        onClick={handleYourDailyTasksClick}
      />

      <TextFieldTextAreaClickable
        title={t('whatWeOffer')}
        value={
          whatWeOffers && whatWeOffers.length > 0
            ? formatArrayToString(whatWeOffers)
            : ''
        }
        placeholder={t('addWhatWeOffer')}
        fullWidth
        icon="editPen"
        textSize="sm"
        iconSize="sm"
        shouldResize
        onClick={handleOurOfferClick}
      />

      <Button
        fullWidth
        fontSize="sm"
        onClick={publish}
        disabled={!category?.subcategory}
      >
        {t('publish')}
      </Button>
    </div>
  );
}
