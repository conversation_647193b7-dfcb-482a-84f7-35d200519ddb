import { CategorySelection } from '@components/ModalsComponents/modals/CategorySelector';

export type ContractType = 'PERNAMENT' | 'B2B';

export interface JobOfferData {
  title: string;
  locations: string[];
  contractType: ContractType;
  minSalary: string;
  maxSalary: string;
  currency: string;
  companySize: string;
  aboutCompany: string | null;
  ourExpectations: string[];
  whatWeOffers: string[];
  yourDailyTasks: string[];
  category?: CategorySelection | null;
}
