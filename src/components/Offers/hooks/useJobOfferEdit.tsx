'use client';

import { useState, useEffect, useRef } from 'react';

import { useModal } from '@components/ModalsComponents/context/ModalContext';
import { CategorySelection } from '@components/ModalsComponents/modals/CategorySelector';

import { JobOfferData, ContractType } from './types';

export const useJobOfferEdit = (initialData?: Partial<JobOfferData>) => {
  const { openModal } = useModal();

  const [offerData, setOfferData] = useState<Partial<JobOfferData>>({
    ...initialData,
  });

  const isInitialized = useRef(false);

  useEffect(() => {
    if (!isInitialized.current && initialData) {
      setOfferData((prev) => ({
        ...prev,
        ...initialData,
      }));
      isInitialized.current = true;
    }
  }, [initialData]);

  const handleSetTitle = (value: string) => {
    setOfferData((prev) => ({ ...prev, title: value }));
  };

  const handleSetLocation = (value: string[]) => {
    setOfferData((prev) => ({ ...prev, locations: value }));
  };

  const handleSetContractType = (value: ContractType) => {
    setOfferData((prev) => ({ ...prev, contractType: value }));
  };

  const handleSetMinSalary = (value: string) => {
    setOfferData((prev) => ({ ...prev, minSalary: value }));
  };

  const handleSetMaxSalary = (value: string) => {
    setOfferData((prev) => ({ ...prev, maxSalary: value }));
  };

  const handleSetCompanySize = (value: string) => {
    setOfferData((prev) => ({ ...prev, companySize: value }));
  };

  const handleSetAboutUs = (value: string | null) => {
    setOfferData((prev) => ({ ...prev, aboutCompany: value }));
  };

  const handleSetOurExpectations = (value: string[]) => {
    setOfferData((prev) => ({ ...prev, ourExpectations: value }));
  };

  const handleSetOurOffer = (value: string[]) => {
    setOfferData((prev) => ({ ...prev, whatWeOffers: value }));
  };

  const handleSetYourDailyTasks = (value: string[]) => {
    setOfferData((prev) => ({ ...prev, yourDailyTasks: value }));
  };

  const handleSetCategory = (value: CategorySelection | null) => {
    setOfferData((prev) => ({ ...prev, category: value }));
  };

  const formatArrayToString = (items: string[]) => {
    return items.map((item) => `• ${item}`).join('\n');
  };

  const handleTitleSalaryClick = () => {
    const modalInitialData = {
      title: offerData.title,
      minSalary: offerData.minSalary,
      maxSalary: offerData.maxSalary,
      currency: offerData.currency,
      category: offerData.category,
    };

    openModal(
      'edit_title_salary',
      [],
      null,
      (data) => {
        handleSetTitle(data.title);
        handleSetMinSalary(data.minSalary);
        handleSetMaxSalary(data.maxSalary);
        handleSetCategory(data.category);
      },
      modalInitialData
    );
  };

  const handleLocationClick = () => {
    openModal(
      'location_selector',
      [],
      null,
      handleSetLocation,
      offerData.locations || []
    );
  };

  const handleCompanySizeClick = () => {
    openModal(
      'company_size_selector',
      [],
      null,
      handleSetCompanySize,
      offerData.companySize
    );
  };

  const handleContractTypeClick = () => {
    openModal(
      'select_type_of_contract',
      [],
      null,
      handleSetContractType,
      offerData.contractType
    );
  };

  const handleAboutUsClick = () => {
    openModal('about_us', [], null, handleSetAboutUs, offerData.aboutCompany);
  };

  const handleOurExpectationsClick = () => {
    openModal(
      'expectations',
      [],
      null,
      handleSetOurExpectations,
      offerData.ourExpectations
    );
  };

  const handleOurOfferClick = () => {
    openModal('offer', [], null, handleSetOurOffer, offerData.whatWeOffers);
  };

  const handleYourDailyTasksClick = () => {
    openModal(
      'daily_tasks',
      [],
      null,
      handleSetYourDailyTasks,
      offerData.yourDailyTasks
    );
  };

  return {
    offerData,
    setOfferData,
    formatArrayToString,
    // Handlery do ustawiania wartości
    handleSetTitle,
    handleSetLocation,
    handleSetContractType,
    handleSetMinSalary,
    handleSetMaxSalary,
    handleSetCompanySize,
    handleSetAboutUs,
    handleSetOurExpectations,
    handleSetOurOffer,
    handleSetCategory,
    handleSetYourDailyTasks,
    // Handlery do kliknięć w pola
    handleLocationClick,
    handleContractTypeClick,
    handleCompanySizeClick,
    handleAboutUsClick,
    handleOurExpectationsClick,
    handleOurOfferClick,
    handleTitleSalaryClick,
    handleYourDailyTasksClick,
  };
};
