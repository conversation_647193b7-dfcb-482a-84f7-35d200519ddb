import { ContractType } from './hooks/types';

type Location = {
  id: number;
  address: string;
  city: {
    id: number;
    name: string;
    province: {
      id: number;
      name: string;
    };
  };
};

export type OfferProps = {
  assignedTo: {
    id: number;
    headline: string;
    logo: string;
    name: string;
    size: string;
  };
  contractType: string;
  createdDate: string;
  currency: string;
  id: number;
  locations: Location[];
  maxSalary: string;
  minSalary: string;
  position: string;
};

export type OffersResponse = {
  items: OfferProps[];
  totalItems: number;
  currentPage: number;
  totalPages: number;
};

export type OfferDetailedProps = OfferProps & {
  createdBy: {
    uuid: string;
    email: string;
    firstName: string;
    lastName: string;
    headline: string;
    avatar: string;
  };
  aboutCompany: string;
  yourDailyTasks: string[];
  ourExpectations: string[];
  whatWeOffers: string[];
  currency: string;
  contractType: ContractType;
  category: {
    id: number;
    name: string;
    subcategories: [string];
    offers: [string];
  };
  locations: [
    {
      id: number;
      city: {
        id: number;
        name: string;
        province: {
          id: number;
          name: string;
          cities: [string];
        };
      };
      address: string;
    },
  ];
};
