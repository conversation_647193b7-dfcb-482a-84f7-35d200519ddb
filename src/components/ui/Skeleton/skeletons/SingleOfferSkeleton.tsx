import { Skeleton } from '../SkeletonWrapper';

export function SingleOfferSkeleton() {
  return (
    <div className="flex min-h-max min-w-[350px] items-center gap-2 overflow-hidden rounded-[30px] bg-layout-box-bg p-3 backdrop-blur-default">
      <Skeleton height={64} width={100} style={{ borderRadius: '20px' }} />
      <div className="flex-1 space-y-2">
        <div className="flex items-center gap-2">
          <Skeleton width={100} height={16} />
          <Skeleton width={60} height={20} style={{ borderRadius: '15px' }} />
        </div>
        <Skeleton width={160} height={20} />
        <div className="flex gap-2">
          <div className="flex items-center gap-1">
            <Skeleton width={16} height={16} style={{ borderRadius: '50%' }} />
            <Skeleton width={80} height={16} />
          </div>
          <div className="flex items-center gap-1">
            <Skeleton width={16} height={16} style={{ borderRadius: '50%' }} />
            <Skeleton width={100} height={16} />
          </div>
        </div>
      </div>
    </div>
  );
}
