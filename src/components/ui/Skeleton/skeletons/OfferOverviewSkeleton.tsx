import { Skeleton } from '../SkeletonWrapper';

export function OfferOverviewSkeleton() {
  return (
    <div className="flex-1 rounded-[30px] bg-layout-box-bg backdrop-blur-default">
      <div className="flex flex-col justify-center gap-4 overflow-visible p-4">
        <div className="flex items-center gap-3">
          <Skeleton height={64} width={100} style={{ borderRadius: '20px' }} />
          <div className="flex-1 space-y-2">
            <Skeleton width={120} height={16} />
            <div className="flex items-center gap-3">
              <Skeleton width={200} height={24} />
              <Skeleton
                width={24}
                height={24}
                style={{ borderRadius: '50%' }}
              />
            </div>
          </div>
          <Skeleton width={32} height={32} style={{ borderRadius: '50%' }} />
        </div>

        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-2 rounded-xl border border-tf-border p-3">
            <Skeleton width={60} height={12} />
            <Skeleton width={100} height={20} />
          </div>
          <div className="space-y-2 rounded-xl border border-tf-border p-3">
            <Skeleton width={60} height={12} />
            <Skeleton width={120} height={20} />
          </div>
          <div className="space-y-2 rounded-xl border border-tf-border p-3">
            <Skeleton width={80} height={12} />
            <Skeleton width={60} height={20} />
          </div>
          <div className="space-y-2 rounded-xl border border-tf-border p-3">
            <Skeleton width={60} height={12} />
            <Skeleton width={100} height={20} />
          </div>
        </div>

        <div className="space-y-2 rounded-xl border border-tf-border p-4">
          <Skeleton width={100} height={12} />
          <div className="space-y-2">
            <Skeleton width="100%" height={16} />
            <Skeleton width="80%" height={16} />
            <Skeleton width="75%" height={16} />
          </div>
        </div>

        <div className="space-y-2 rounded-xl border border-tf-border p-4">
          <Skeleton width={80} height={12} />
          <div className="space-y-2">
            <Skeleton width="100%" height={16} />
            <Skeleton width="85%" height={16} />
            <Skeleton width="80%" height={16} />
          </div>
        </div>

        <div className="space-y-2 rounded-xl border border-tf-border p-4">
          <Skeleton width={100} height={12} />
          <div className="space-y-2">
            <Skeleton width="100%" height={16} />
            <Skeleton width="75%" height={16} />
            <Skeleton width="85%" height={16} />
          </div>
        </div>

        <div className="space-y-2 rounded-xl border border-tf-border p-4">
          <Skeleton width={110} height={12} />
          <div className="space-y-2">
            <Skeleton width="100%" height={16} />
            <Skeleton width="80%" height={16} />
            <Skeleton width="60%" height={16} />
          </div>
        </div>

        <div className="flex justify-center">
          <Skeleton width={240} height={48} style={{ borderRadius: '20px' }} />
        </div>
      </div>
    </div>
  );
}
