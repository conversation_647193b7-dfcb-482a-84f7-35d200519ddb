import { borderRadiusStyles } from '@app/elements/_shared';

import { Skeleton } from '../SkeletonWrapper';

export function CompanyViewSkeleton() {
  return (
    <div
      className={`mx-auto flex size-full max-w-4xl flex-col gap-4 ${borderRadiusStyles.extra}`}
    >
      <div className="flex w-full">
        <div className="flex shrink-0 items-center">
          <Skeleton width={48} height={48} style={{ borderRadius: '20px' }} />
        </div>
        <div className="flex w-full min-w-0 shrink items-center gap-2 rounded-xl bg-layout-box-bg p-1.5">
          <div className="flex w-full gap-2">
            <Skeleton height={40} style={{ flex: 1, borderRadius: '12px' }} />
            <Skeleton height={40} style={{ flex: 1, borderRadius: '12px' }} />
            <Skeleton height={40} style={{ flex: 1, borderRadius: '12px' }} />
          </div>
        </div>
      </div>

      <div className="no-scrollbar w-full overflow-y-auto">
        <div
          className={`mx-auto flex flex-col gap-4 bg-layout-box-bg backdrop-blur-default ${borderRadiusStyles.extra} p-6`}
        >
          <div className="flex justify-between gap-4 rounded-2xl">
            <Skeleton
              width={240}
              height={85}
              style={{ borderRadius: '20px' }}
            />
            <div className="flex-1 space-y-2 rounded-xl border border-tf-border p-3">
              <Skeleton width={100} height={12} />
              <Skeleton width={200} height={24} />
            </div>
          </div>

          <div className="flex gap-4">
            <div className="flex-1 space-y-2 rounded-xl border border-tf-border p-3">
              <Skeleton width={80} height={12} />
              <Skeleton width={60} height={20} />
            </div>
            <div className="flex-1 space-y-2 rounded-xl border border-tf-border p-3">
              <Skeleton width={60} height={12} />
              <Skeleton width={120} height={20} />
            </div>
          </div>

          <div className="space-y-2 rounded-xl border border-tf-border p-3">
            <Skeleton width={60} height={12} />
            <div className="space-y-2">
              <Skeleton width="100%" height={16} />
              <Skeleton width="80%" height={16} />
              <Skeleton width="75%" height={16} />
            </div>
          </div>

          <div className="flex justify-center">
            <Skeleton
              width={200}
              height={48}
              style={{ borderRadius: '20px' }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
