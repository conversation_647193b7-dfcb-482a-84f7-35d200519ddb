'use client';

import React, { useState, useEffect } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { useTranslations } from 'next-intl';

import { Button, ButtonAsIcon } from '@app/elements/Buttons';
import { ImageComponent } from '@app/elements/ImageComponent';
import { TextFieldWithIcon } from '@app/elements/TextFields';

import { CompanyDataProps, CompanyDetails } from '@components/Company/types';
import { LogoIcon } from '@components/icons/IconWrapper';
import { useModal } from '@components/ModalsComponents/context/ModalContext';

import { useCompany } from '@actions/hooks/useCompany';
import { useCreateCompany } from '@actions/hooks/useCreateCompany';
import { logout } from '@actions/server/logout';
import { useUser } from '@contexts/UserContext';

interface ClientNavbarProp {
  company?: CompanyDetails | null;
}

export function ClientNavbar({ company: initialCompany }: ClientNavbarProp) {
  const { openModal } = useModal();
  const { user } = useUser();
  const t = useTranslations('Navbar');
  const createCompany = useCreateCompany();
  const { data: companyData } = useCompany(
    initialCompany?.company.id,
    initialCompany
  );

  const company = companyData?.data || initialCompany;

  const navigate = useRouter();
  const searchParams = useSearchParams();

  function handleNavigate(newPath: string) {
    navigate.push(newPath);
  }

  const [activeFiltersCount, setActiveFiltersCount] = useState(0);

  useEffect(() => {
    let count = 0;
    const categoryIds = searchParams.get('categoryIds');
    const position = searchParams.get('position');
    const minSalary = searchParams.get('minSalary');
    const maxSalary = searchParams.get('maxSalary');

    if (categoryIds) count++;

    if (position) count++;

    if (minSalary || maxSalary) count++;

    setActiveFiltersCount(count);
  }, [searchParams.toString()]);

  const [search, setSearch] = useState<string>('');

  const handleAddCompany = () => {
    openModal(
      'company_edit',
      [],
      null,
      async (data: CompanyDataProps) => {
        await createCompany(data);
      },
      null
    );
  };

  return (
    <nav className="fixed inset-x-6 top-4 z-10 flex min-h-max flex-col items-center gap-4 overflow-hidden rounded-[30px] bg-layout-box-bg py-4 backdrop-blur-default max-laptop:inset-x-4 max-tablet:inset-x-2 max-tablet:gap-3">
      {/* Upper navbar  */}
      <div className="flex w-full gap-32 px-4 max-desktop:gap-16 max-laptop:gap-8">
        {/* Left side | Logo */}
        <div className="flex min-w-max flex-1 items-center justify-start">
          <LogoIcon
            className="w-auto cursor-pointer"
            onClick={() => handleNavigate('/')}
          />
        </div>

        {/* Middle side  */}
        <div className="flex items-center gap-4">
          <TextFieldWithIcon
            title=""
            placeholder={t('search')}
            input={search}
            setInput={setSearch}
            icon="search"
            iconSize="sm"
            borderRadius="extra"
            padding="lg"
            className="max-desktop:hidden"
          />

          <div className="relative hidden desktop:block">
            <ButtonAsIcon
              borderRadius="extra"
              buttonIcon="filter"
              padding="lg"
              variant="secondary"
              onClick={() => openModal('filter')}
            />
            {activeFiltersCount > 0 && (
              <div className="absolute -right-1 -top-1 flex size-5 items-center justify-center rounded-full bg-btn-primary-bg text-xs font-bold text-white">
                {activeFiltersCount}
              </div>
            )}
          </div>
        </div>

        {/* Right side  */}
        <div className="flex flex-1 items-center justify-end gap-4">
          {user ? (
            <>
              {company ? (
                <>
                  <ImageComponent
                    src={company.company.logo}
                    alt="Company Logo"
                    width={75}
                    height={60}
                    isLogo
                    objectFit="contain"
                    priority
                    className="cursor-pointer"
                    onClick={() =>
                      handleNavigate(`/company/${company.company.id}`)
                    }
                  />

                  <Button
                    padding="lg"
                    borderRadius="extra"
                    className="max-tablet:hidden"
                    onClick={() => handleNavigate('/createOffer')}
                  >
                    {t('add')}
                  </Button>
                </>
              ) : (
                <Button
                  padding="lg"
                  borderRadius="extra"
                  className="max-tablet:hidden"
                  onClick={handleAddCompany}
                >
                  {t('addCompany')}
                </Button>
              )}
              <Button
                padding="lg"
                borderRadius="extra"
                className="max-tablet:hidden"
                onClick={async () => {
                  await logout();
                  navigate.push('/');
                }}
              >
                {t('logout')}
              </Button>
            </>
          ) : (
            <Button
              padding="lg"
              borderRadius="extra"
              className="max-tablet:hidden"
              onClick={() => openModal('login', ['login', 'signin'])}
            >
              {t('login')}
            </Button>
          )}

          <ButtonAsIcon
            borderRadius="extra"
            buttonIcon="navbarMenu"
            padding="lg"
            iconSize="md"
            variant="secondary"
            onClick={() => openModal('menu')}
          />
        </div>
      </div>

      {/* Shows when below laptop resolution  */}
      <hr className="m-0 w-full border-tf-border desktop:hidden" />

      {/* Bottom navbar when on tablet or mobile  */}
      <div className="flex w-full gap-4 px-4 desktop:hidden">
        <TextFieldWithIcon
          title=""
          placeholder={t('search')}
          input={search}
          setInput={setSearch}
          icon="search"
          iconSize="sm"
          borderRadius="extra"
          padding="xl"
          fullWidth
        />

        <div className="relative">
          <ButtonAsIcon
            borderRadius="extra"
            buttonIcon="filter"
            padding="lg"
            iconSize="sm"
            variant="secondary"
            onClick={() => openModal('filter')}
          />
          {activeFiltersCount > 0 && (
            <div className="absolute -right-1 -top-1 flex size-5 items-center justify-center rounded-full bg-btn-primary-bg text-xs font-bold text-white">
              {activeFiltersCount}
            </div>
          )}
        </div>
      </div>
    </nav>
  );
}
