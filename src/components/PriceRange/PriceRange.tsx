import React from 'react';

import { useTranslations } from 'next-intl';

import { TextField } from '@app/elements/TextFields';

import { SalaryIcon } from '@components/icons';

import { PriceRangeProps } from './types';

export default function PriceRange({
  salaryFrom,
  salaryTo,
  setSalaryFrom,
  setSalaryTo,
}: PriceRangeProps) {
  const t = useTranslations('PriceRange');

  return (
    <div className="flex items-center gap-2">
      <TextField
        title={t('salaryFrom')}
        inputType="number"
        input={salaryFrom}
        setInput={setSalaryFrom}
        placeholder={t('salaryFromPlaceholder')}
        borderRadius="rounded"
        className="salary-field"
      />
      <SalaryIcon size="xl" />
      <TextField
        title={t('salaryTo')}
        inputType="number"
        input={salaryTo}
        setInput={setSalaryTo}
        placeholder={t('salaryToPlaceholder')}
        borderRadius="rounded"
        className="salary-field"
      />
    </div>
  );
}
