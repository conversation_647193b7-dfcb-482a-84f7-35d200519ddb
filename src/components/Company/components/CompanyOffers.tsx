import React from 'react';

import { useTranslations } from 'next-intl';

import { Button } from '@app/elements/Buttons';

import { SingleOffer } from '@components/Offers/SingleOffer/SingleOffer';
import { CompanyOffersSkeleton } from '@components/ui/Skeleton/skeletons/CompanyOfferSkeleton';

import {
  JobOffer,
  mapJobOfferToMappedOffer,
  CompanyOffersProps,
} from '../types';

export default function CompanyOffers({
  offers,
  onOpenOffer,
  variant,
  isLoading = false,
}: CompanyOffersProps & { isLoading?: boolean }) {
  const t = useTranslations('Company');

  const handleOpenOffer = (offer: JobOffer) => {
    onOpenOffer(offer);
  };

  if (isLoading) {
    return <CompanyOffersSkeleton count={5} />;
  }

  if (!offers?.length) {
    return (
      <div className="flex flex-col items-center justify-center py-10">
        <p className="text-center text-gray-400">
          {variant === 'active' ? t('noActiveOffers') : t('noExpiredOffers')}
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 overflow-auto">
      {offers.map((offer) => {
        const mappedOffer = mapJobOfferToMappedOffer(offer);

        return (
          <div
            onClick={() => handleOpenOffer(offer)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                handleOpenOffer(offer);
              }
            }}
            role="button"
            tabIndex={0}
            key={offer.id}
            className="relative"
          >
            <SingleOffer offer={mappedOffer} />
            <Button
              className="absolute right-4 top-1/2 hidden -translate-y-1/2 rounded-3xl px-5 py-2 tablet:block"
              // PO WYBRANIU OFERTY DO ADRESU ZOSTAJE DOPISANY 'activeOffer' - PRZY WPINANIU BACKEDNU PEWNIE TRZEBA DODAĆ PROPSA ZE W TYM PRZYPADKU MA BYC JAKIES ID PEWNIE
              onClick={(e) => {
                e.stopPropagation();
                handleOpenOffer(offer);
              }}
            >
              {t('open')}
            </Button>
          </div>
        );
      })}
    </div>
  );
}
