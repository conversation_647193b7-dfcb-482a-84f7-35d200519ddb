import React, { useEffect, useRef, useState } from 'react';

import { useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';

import { borderRadiusStyles, paddings } from '@app/elements/_shared';
import { Button } from '@app/elements/Buttons';
import { ImageComponent } from '@app/elements/ImageComponent';
import { Placeholder } from '@app/elements/Placeholder';
import { TextFieldReadOnly } from '@app/elements/TextFields';

import { useModal } from '@components/ModalsComponents/context/ModalContext';

import { useUpdateCompany } from '@actions/hooks/useUpdateCompany';
import { useUpdateCompanyLogo } from '@actions/hooks/useUpdateCompanyLogo';

import { CompanyDataProps, CompanyProfileProps } from '../types';

export default function CompanyProfile({
  id,
  name: initialName = '',
  website: initialWebsite = '',
  size: initialSize = '',
  aboutCompany: initialAboutCompany = '',
  logo: initialLogo = '',
}: CompanyProfileProps) {
  const { openModal } = useModal();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const updateCompany = useUpdateCompany();
  const updateCompanyLogo = useUpdateCompanyLogo();
  const queryClient = useQueryClient();

  const t = useTranslations('Company');

  const logoUrl =
    initialLogo &&
    (initialLogo.startsWith('http')
      ? initialLogo
      : `${process.env.NEXT_PUBLIC_API_URL}/files/${initialLogo}`);

  const [companyData, setCompanyData] = useState<CompanyDataProps>({
    name: initialName,
    website: initialWebsite,
    size: initialSize,
    aboutCompany: initialAboutCompany,
    logo: logoUrl,
  });

  useEffect(() => {
    setCompanyData({
      name: initialName,
      website: initialWebsite,
      size: initialSize,
      aboutCompany: initialAboutCompany,
      logo: logoUrl,
    });
  }, [initialName, initialWebsite, initialSize, initialAboutCompany, logoUrl]);

  const { name, website, size, aboutCompany, logo } = companyData;

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setCompanyData({
        ...companyData,
        logo: imageUrl,
      });

      await updateCompanyLogo(file, id);

      await queryClient.invalidateQueries({
        queryKey: ['getCompany', { id: String(id) }],
      });
    }
  };

  const companyDataToEdit = { ...companyData };
  delete companyDataToEdit.logo;

  const handleOpenModal = () => {
    openModal(
      'company_edit',
      [],
      null,
      async (updateData: CompanyDataProps) => {
        setCompanyData(updateData);
        await updateCompany({
          companyId: id,
          ...updateData,
        });
        await queryClient.invalidateQueries({
          queryKey: ['getCompany', { id: String(id) }],
        });
      },
      companyDataToEdit
    );
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      fileInputRef.current?.click();
    }
  };

  return (
    <div className="no-scrollbar w-full overflow-y-auto">
      <div
        className={`mx-auto flex flex-col gap-4 bg-layout-box-bg backdrop-blur-default ${borderRadiusStyles.extra} ${paddings.lg}`}
      >
        <div className="flex justify-between gap-4 rounded-2xl">
          <div
            className="relative h-[85px] w-[240px] cursor-pointer"
            onClick={() => fileInputRef.current?.click()}
            onKeyDown={handleKeyPress}
            tabIndex={0}
            aria-label={t('companyPictureAlt')}
            role="button"
          >
            {/* {tutaj w lighthouse pokazuje slaby performance do ogarnięcia te Image w całej apce ogólnie} */}
            {!logo ? (
              <Placeholder />
            ) : (
              <ImageComponent
                src={logo}
                alt={t('companyLogo')}
                fill
                isLogo
                unoptimized={false}
                quality={75}
                width={240}
                height={85}
                priority
                objectFit="contain"
              />
            )}

            <div className="absolute inset-0 flex items-center justify-center rounded-[20px] bg-[#37373760] transition-colors duration-300 hover:bg-[#37373740]">
              <div className="opacity-100">
                <Placeholder iconOnly />
              </div>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleImageChange}
            />
          </div>

          <TextFieldReadOnly
            title={t('companyName')}
            titleValue={name || t('addCompanyName')}
            fullWidth
            textSize="lg"
          />
        </div>

        <div className="flex gap-4">
          <TextFieldReadOnly
            title={t('companySize')}
            titleValue={size ? `${size}` : t('addCompanySize')}
            fullWidth
          />
          <TextFieldReadOnly
            title={t('website')}
            titleValue={website || t('addWebsite')}
            fullWidth
          />
        </div>

        <TextFieldReadOnly
          title={t('aboutUs')}
          titleValue={aboutCompany || t('addAboutUs')}
          fullWidth
        />

        <Button
          onClick={handleOpenModal}
          className="mx-auto"
          padding="lg"
          borderRadius="extra"
        >
          {t('editCompanyDetails')}
        </Button>
        <div />
      </div>
    </div>
  );
}
