import './globals.css';
import { NextIntlClientProvider } from 'next-intl';
import { getLocale, getMessages } from 'next-intl/server';

import { Hydration } from '@components/Hydration';
import { Body, ScrollableContent } from '@components/Layout';
import { ModalProvider } from '@components/ModalsComponents/context/ModalContext';
import { ModalLayout } from '@components/ModalsComponents/layout';
import Navbar from '@components/Navbar/Navbar';
import { QueryProvider } from '@components/QueryProvider';
import { SkeletonWrapper } from '@components/ui/Skeleton/SkeletonWrapper';
import { Toaster } from '@components/ui/toaster';

import { getLoggedUser } from '@actions/server/getLoggedUser';
import { UserProvider } from '@contexts/UserContext';

import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();
  const messages = await getMessages();
  const user = await getLoggedUser();

  return (
    <QueryProvider>
      <Hydration>
        <ModalProvider>
          <html lang={locale}>
            <Body>
              <NextIntlClientProvider messages={messages}>
                <UserProvider user={user}>
                  <SkeletonWrapper>
                    <ModalLayout />
                    <Navbar />
                    <Toaster />
                    <ScrollableContent>{children}</ScrollableContent>
                  </SkeletonWrapper>
                </UserProvider>
              </NextIntlClientProvider>
            </Body>
          </html>
        </ModalProvider>
      </Hydration>
    </QueryProvider>
  );
}
