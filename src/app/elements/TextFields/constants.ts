// Body of Textfield (first div)
const defaultTextFieldWrapper =
  'border border-tf-border transition duration-default ease-in-out focus-within:border-tf-border-focus';

const defaultTextFieldWrapperWithoutFocus = 'border border-tf-border';

// Title
const defaultTextFieldTitle = `text-tf-text-title opacity-70 text-nowrap`;

// Input
const defaultTextFieldInput = `w-full bg-transparent font-semibold text-tf-text-content outline-none placeholder:text-tf-text-content focus-visible:ring-0 p-0 border-none shadow-none transition-all duration-300 placeholder:opacity-30 placeholder:transition-opacity placeholder:duration-300 placeholder:ease-in-out focus:placeholder:opacity-0   [&::-webkit-inner-spin-button]:opacity-100
  [&::-webkit-inner-spin-button]:bg-transparent
  [&::-webkit-inner-spin-button]:cursor-pointer
  [&::-webkit-inner-spin-button]:h-10
  [&::-webkit-inner-spin-button]:mr-1
  [&::-webkit-outer-spin-button]:opacity-100
  [&::-webkit-outer-spin-button]:bg-transparent
  [&::-webkit-outer-spin-button]:cursor-pointer
  [&::-webkit-outer-spin-button]:h-10`;

// Default max width of text field
const textFieldMaxWidth = 'max-w-tf';

export {
  defaultTextFieldWrapper,
  defaultTextFieldWrapperWithoutFocus,
  defaultTextFieldTitle,
  defaultTextFieldInput,
  textFieldMaxWidth,
};
