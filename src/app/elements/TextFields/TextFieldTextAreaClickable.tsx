import { Text } from '@app/elements/Texts';

import { Icons, IconName } from '@components/icons';
import { Textarea as UITextarea } from '@components/ui/textarea';

import { borderRadiusStyles, paddings, fontSizes, iconSizes } from '../_shared';
import {
  defaultTextFieldWrapper,
  textFieldMaxWidth,
  defaultTextFieldTitle,
  defaultTextFieldInput,
} from './constants';
import { TextFieldTextAreaClickableProps } from './types';

export function TextFieldTextAreaClickable({
  title,
  placeholder,
  value = '',
  fullWidth = false,
  titleSize = 'sm',
  textSize = 'md',
  padding = 'md',
  borderRadius = 'extra',
  className = '',
  autoFocus = false,
  onClick,
  iconSize = 'md',
  icon = null,
  shouldResize = false,
}: TextFieldTextAreaClickableProps) {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if ((e.key === 'Enter' || e.key === ' ') && onClick) {
      onClick();
    }
  };

  const IconComponent = icon ? Icons[icon as IconName] : null;

  const getRows = () => {
    if (!shouldResize) {
      return undefined;
    }

    if (typeof value === 'string') {
      return value.split('\n').length;
    }

    if (Array.isArray(value)) {
      return value.length;
    }

    return undefined;
  };
  return (
    <div
      role="button"
      tabIndex={onClick ? 0 : undefined}
      onClick={() => onClick && onClick()}
      onKeyDown={handleKeyDown}
      className={` ${fullWidth ? 'w-full' : textFieldMaxWidth} relative flex h-max cursor-pointer items-center justify-between gap-2 ${defaultTextFieldWrapper} ${paddings[padding]} ${className} ${borderRadiusStyles[borderRadius]}`}
    >
      <div className="w-full">
        <Text className={`${defaultTextFieldTitle} ${fontSizes[titleSize]}`}>
          {title}
        </Text>

        <UITextarea
          autoFocus={autoFocus}
          placeholder={placeholder}
          className={`${defaultTextFieldInput} ${fontSizes[textSize]} no-scrollbar auto-height-textarea min-h-[125px] cursor-pointer`}
          value={value}
          rows={getRows()}
          readOnly
        />
      </div>

      {/* Icon */}
      {icon && IconComponent && (
        <div
          role="button"
          tabIndex={0}
          onKeyDown={handleKeyDown}
          onClick={onClick}
          className="static right-0 flex h-full cursor-pointer items-center  justify-center"
        >
          <IconComponent size={iconSize} className="ml-2" />
        </div>
      )}
    </div>
  );
}
