import { Text } from '@app/elements/Texts';

import { Icons, IconName } from '@components/icons';
import { Textarea as UITextarea } from '@components/ui/textarea';

import { borderRadiusStyles, paddings, fontSizes, iconSizes } from '../_shared';
import {
  defaultTextFieldWrapper,
  textFieldMaxWidth,
  defaultTextFieldTitle,
  defaultTextFieldInput,
  defaultTextFieldWrapperWithoutFocus,
} from './constants';
import { TextFieldTextAreaProps } from './types';

export function TextFieldTextArea({
  title,
  placeholder,
  value = '',
  setValue,
  fullWidth = false,
  titleSize = 'sm',
  textSize = 'md',
  padding = 'md',
  borderRadius = 'extra',
  className = '',
  autoFocus = false,
  icon = null,
  iconSize = 'md',
  onClick,
  disabledUserActivity,
}: TextFieldTextAreaProps) {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (onClick && (e.key === 'Enter' || e.key === ' ')) {
      onClick();
    }
  };

  const IconComponent = icon ? Icons[icon as IconName] : null;

  const disabledInputAction = disabledUserActivity
    ? 'select-none, pointer-events-none '
    : '';

  return (
    <div
      tabIndex={onClick ? 0 : undefined}
      onClick={() => onClick && onClick()}
      onKeyDown={handleKeyDown}
      role="button"
      className={` ${disabledInputAction ? defaultTextFieldWrapperWithoutFocus : defaultTextFieldWrapper} ${fullWidth ? 'w-full' : textFieldMaxWidth} relative flex h-max items-center justify-between gap-2 ${paddings[padding]} ${className} ${borderRadiusStyles[borderRadius]}`}
    >
      <div className="w-full">
        <Text className={`${defaultTextFieldTitle} ${fontSizes[titleSize]}`}>
          {title}
        </Text>

        <UITextarea
          autoFocus={autoFocus}
          placeholder={placeholder}
          className={`${defaultTextFieldInput} ${fontSizes[textSize]} ${disabledInputAction} min-h-[300px] `}
          onChange={(e) => setValue && setValue(e.target.value)}
          value={value}
        />
      </div>

      {/* Icon */}
      {icon && IconComponent && (
        <div
          role="button"
          tabIndex={0}
          onKeyDown={handleKeyDown}
          onClick={onClick}
          className="static right-0 flex h-full cursor-pointer items-center justify-center"
        >
          <IconComponent size={iconSize} className="ml-2" />
        </div>
      )}
    </div>
  );
}
