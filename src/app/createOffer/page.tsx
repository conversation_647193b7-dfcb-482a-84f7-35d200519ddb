import { getTranslations } from 'next-intl/server';

import CreateOffer from '@components/Offers/CreateOffer/CreateOffer';

import { getLoggedUserCompany } from '@actions/server/getLoggedUserCompany';

export default async function OffersPage() {
  const data = await getLoggedUserCompany();
  const t = await getTranslations('Company');

  if (!data?.data) {
    return (
      <div className="flex justify-center p-10">{t('companyNotFound')}</div>
    );
  }

  return (
    <div className="flex h-full items-start justify-center p-4">
      <CreateOffer companyData={data.data} />
    </div>
  );
}
